import java.sql.*;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.nio.charset.StandardCharsets;
import java.io.*;

public class UniversityManagementSystem {
    // ANSI Color Codes for UI Enhancement (Using softer tones)
    public static final String RESET = "\u001B[0m";
    public static final String RED = "\u001B[31m";        // Errors - Generally okay
    public static final String GREEN = "\u001B[32m";      // Success - Generally okay
    public static final String YELLOW = "\u001B[33m";     // Warnings/Highlights - Generally okay
    public static final String BLUE = "\u001B[34m";       // Professor <PERSON> - Generally okay
    // Changed PURPLE (35m) to Bright Magenta (95m) for a softer look
    public static final String SOFTER_MAGENTA = "\u001B[95m";
    // Changed CYAN (36m) to Bright Blue (94m) for a softer look
    public static final String SOFTER_BLUE = "\u001B[94m";
    public static final String WHITE = "\u001B[37m";      // General text - Generally okay
    public static final String BOLD = "\u001B[1m";

    private static final String DB_URL = "**************************************";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "";
    private static Connection connection;
    private static Scanner scanner = new Scanner(System.in);
    private static int currentUserId = -1;
    private static String currentUserRole = "";

    // Utility method for hashing passwords
    private static String hashPassword(String password) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(password.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) hexString.append('0');
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            System.err.println(RED + "Error hashing password: Algorithm not found." + RESET);
            return null; // Should ideally handle this more gracefully
        }
    }

    // Utility method to create directories
    private static void createExportDirectory(String path) {
        File dir = new File(path);
        if (!dir.exists()) {
            if (dir.mkdirs()) {
                System.out.println(GREEN + "Created export directory: " + path + RESET);
            } else {
                System.err.println(RED + "Failed to create export directory: " + path + RESET);
            }
        }
    }

    // Utility method to write data to a file
    private static boolean writeToFile(String filePath, String data) {
        try (PrintWriter writer = new PrintWriter(new FileWriter(filePath))) {
            writer.print(data);
            return true;
        } catch (IOException e) {
            System.err.println(RED + "Error writing to file: " + e.getMessage() + RESET);
            return false;
        }
    }

    public static void main(String[] args) {
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            connection = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
            initializeDatabase();
            // Use SOFTER_BLUE for the main banner text
            System.out.println(BOLD + "" +
                    "██╗   ██╗███╗   ██╗██╗      ██████╗  ██████╗ ████████╗██╗ ██████╗███████╗\n" +
                    "██║   ██║████╗  ██║██║      ██╔══██╗██╔═══██╗╚══██╔══╝██║██╔════╝██╔════╝\n" +
                    "██║   ██║██╔██╗ ██║██║█████╗██████╔╝██║   ██║   ██║   ██║██║     ███████╗\n" +
                    "██║   ██║██║╚██╗██║██║╚════╝██╔══██╗██║   ██║   ██║   ██║██║     ╚════██║\n" +
                    "╚██████╔╝██║ ╚████║██║      ██████╔╝╚██████╔╝   ██║   ██║╚██████╗███████║\n" +
                    " ╚═════╝ ╚═╝  ╚═══╝╚═╝      ╚═════╝  ╚═════╝    ╚═╝   ╚═╝ ╚═════╝╚══════╝\n" +
                    "                                                                          "

                            + RESET);
            // Use SOFTER_BLUE for the connecting message
            System.out.println(SOFTER_BLUE + "Connecting to database..." + RESET);
            animateLoading();
            while (true) {
                if (currentUserId == -1) {
                    showLoginMenu();
                } else {
                    switch (currentUserRole) {
                        case "admin":
                            showAdminMenu();
                            break;
                        case "professor":
                            showProfessorMenu();
                            break;
                        case "student":
                            showStudentMenu();
                            break;
                    }
                }
            }
        } catch (Exception e) {
            System.err.println(RED + "System error: " + e.getMessage() + RESET);
            e.printStackTrace(); // For debugging, can be removed later
        } finally {
            try {
                if (connection != null && !connection.isClosed()) {
                    connection.close();
                }
            } catch (SQLException e) {
                System.err.println(RED + "Error closing database connection: " + e.getMessage() + RESET);
            }
        }
    }

    private static void initializeDatabase() throws SQLException {
        Statement stmt = connection.createStatement();
        // Create tables with improved constraints
        stmt.execute("CREATE TABLE IF NOT EXISTS users (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "username VARCHAR(50) UNIQUE NOT NULL, " + // Added NOT NULL
                "password VARCHAR(64) NOT NULL, " + // Increased size for hashed password, NOT NULL
                "role ENUM('admin', 'professor', 'student') NOT NULL, " + // Added NOT NULL
                "name VARCHAR(100) NOT NULL)"); // Added NOT NULL

        stmt.execute("CREATE TABLE IF NOT EXISTS courses (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "code VARCHAR(10) UNIQUE NOT NULL, " + // Added NOT NULL
                "name VARCHAR(100) NOT NULL, " + // Added NOT NULL
                "credits INT NOT NULL, " + // Added NOT NULL
                "professor_id INT, " +
                "FOREIGN KEY (professor_id) REFERENCES users(id) ON DELETE SET NULL)"); // Added Foreign Key Constraint

        stmt.execute("CREATE TABLE IF NOT EXISTS enrollments (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "student_id INT NOT NULL, " + // Added NOT NULL
                "course_id INT NOT NULL, " + // Added NOT NULL
                "grade VARCHAR(2), " +
                "FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE, " + // Added Foreign Key Constraint
                "FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE, " + // Added Foreign Key Constraint
                "UNIQUE KEY unique_enrollment (student_id, course_id))"); // Prevent duplicate enrollments

        stmt.execute("CREATE TABLE IF NOT EXISTS notifications (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "user_id INT NOT NULL, " + // Added NOT NULL
                "message TEXT NOT NULL, " + // Added NOT NULL
                "timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "is_read BOOLEAN DEFAULT FALSE, " +
                "FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE)"); // Added Foreign Key Constraint

        // Create messages table for enhanced messaging
        stmt.execute("CREATE TABLE IF NOT EXISTS messages (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "sender_id INT NOT NULL, " +
                "receiver_id INT NOT NULL, " +
                "subject VARCHAR(255), " +
                "content TEXT NOT NULL, " +
                "timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "is_read BOOLEAN DEFAULT FALSE, " +
                "FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE, " +
                "FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE)");

        // Create default admin if not exists (with hashed password)
        PreparedStatement checkAdmin = connection.prepareStatement(
                "SELECT COUNT(*) FROM users WHERE role='admin'");
        ResultSet rs = checkAdmin.executeQuery();
        rs.next();
        if (rs.getInt(1) == 0) {
            String hashedPassword = hashPassword("admin123");
            if (hashedPassword != null) {
                PreparedStatement createAdmin = connection.prepareStatement(
                        "INSERT INTO users (username, password, role, name) VALUES (?, ?, ?, ?)");
                createAdmin.setString(1, "admin");
                createAdmin.setString(2, hashedPassword); // Store hashed password
                createAdmin.setString(3, "admin");
                createAdmin.setString(4, "System Administrator");
                createAdmin.execute();
                System.out.println(GREEN + "Default admin user created." + RESET);
            } else {
                System.err.println(RED + "Failed to create default admin user due to password hashing error." + RESET);
            }
        }
    }

    // --- Utility Methods for Input Validation ---
    private static String getStringInput(String prompt) {
        System.out.print(prompt);
        String input = scanner.nextLine().trim();
        while (input.isEmpty()) {
            System.out.print(RED + "Input cannot be empty. " + prompt + RESET);
            input = scanner.nextLine().trim();
        }
        return input;
    }

    private static int getIntInput(String prompt) {
        System.out.print(prompt);
        while (true) {
            try {
                String input = scanner.nextLine().trim();
                if (input.isEmpty()) {
                    System.out.print(RED + "Input cannot be empty. " + prompt + RESET);
                    continue;
                }
                return Integer.parseInt(input);
            } catch (NumberFormatException e) {
                System.out.print(RED + "Invalid number format. " + prompt + RESET);
            }
        }
    }

    private static String getValidatedRoleInput(String prompt) {
        System.out.print(prompt);
        while (true) {
            String input = scanner.nextLine().trim().toLowerCase();
            if (input.isEmpty()) {
                System.out.print(RED + "Input cannot be empty. " + prompt + RESET);
                continue;
            }
            if (input.equals("admin") || input.equals("professor") || input.equals("student")) {
                return input;
            } else {
                System.out.print(RED + "Invalid role. Please enter 'admin', 'professor', or 'student'. " + prompt + RESET);
            }
        }
    }

    private static String getValidatedGradeInput(String prompt) {
        System.out.print(prompt);
        while (true) {
            String input = scanner.nextLine().trim().toUpperCase();
            if (input.isEmpty()) {
                System.out.print(RED + "Input cannot be empty. " + prompt + RESET);
                continue;
            }
            if (input.matches("[A-F][+-]?") || input.equals("F")) { // Basic grade validation (A-F, A+, B-, etc.)
                return input;
            } else {
                System.out.print(RED + "Invalid grade format. Please enter a valid grade (e.g., A, B+, C-, F). " + prompt + RESET);
            }
        }
    }

    private static boolean getConfirmation(String prompt) {
        System.out.print(YELLOW + prompt + " (y/n): " + RESET);
        String input = scanner.nextLine().trim().toLowerCase();
        return input.startsWith("y");
    }
    // --- End Utility Methods ---

    private static void showLoginMenu() {
        clearScreen();
        // Use SOFTER_BLUE for the login header
        System.out.println(BOLD + SOFTER_BLUE + "🎓 UNIVERSITY LOGIN" + RESET);
        System.out.println(BOLD + "==================" + RESET);
        System.out.println("1. Login");
        System.out.println("0. Exit System");
        int choice = getIntInput(" Select option: ");
        if (choice == 0) {
            System.out.println(GREEN + "👋 Goodbye!" + RESET);
            System.exit(0);
        } else if (choice == 1) {
            String username = getStringInput("Username: ");
            String password = getStringInput("Password: ");
            String hashedPassword = hashPassword(password); // Hash input password for comparison
            try {
                PreparedStatement stmt = connection.prepareStatement(
                        "SELECT id, role FROM users WHERE username=? AND password=?");
                stmt.setString(1, username);
                stmt.setString(2, hashedPassword); // Compare with hashed password
                ResultSet rs = stmt.executeQuery();
                if (rs.next()) {
                    currentUserId = rs.getInt("id");
                    currentUserRole = rs.getString("role");
                    System.out.println(GREEN + "✅ Login successful! Welcome " + currentUserRole + RESET);
                    pressEnterToContinue();
                } else {
                    System.out.println(RED + "❌ Invalid credentials!" + RESET);
                    pressEnterToContinue();
                }
            } catch (SQLException e) {
                System.err.println(RED + "Login error: " + e.getMessage() + RESET);
            }
        } else {
            System.out.println(RED + "Invalid option!" + RESET);
            pressEnterToContinue();
        }
    }

    private static void showAdminMenu() {
        clearScreen();
        // Use SOFTER_MAGENTA for the admin header
        System.out.println(BOLD + SOFTER_MAGENTA + "👑 ADMINISTRATOR DASHBOARD" + RESET);
        System.out.println(BOLD + "==========================" + RESET);
        System.out.println("1. Manage Users");
        System.out.println("2. Manage Courses");
        System.out.println("3. View All Enrollments");
        System.out.println("4. Send Notification");
        System.out.println("5. View Notifications");
        System.out.println("6. Chatbot Assistant");
        System.out.println("7. Send Message");
        System.out.println("8. Export Data");
        System.out.println("0. Logout");
        int choice = getIntInput(" Select option: ");
        switch (choice) {
            case 1: manageUsers(); break;
            case 2: manageCourses(); break;
            case 3: viewAllEnrollments(); break;
            case 4: sendNotification(); break;
            case 5: viewNotifications(); break;
            case 6: chatbotAssistant(); break;
            case 7: sendMessage(); break; // New messaging option
            case 8: exportDataMenu(); break; // New export option
            case 0: logout(); break;
            default: System.out.println(RED + "Invalid option!" + RESET); pressEnterToContinue();
        }
    }

    private static void showProfessorMenu() {
        clearScreen();
        // Use BLUE for the professor header (keeping it as is)
        System.out.println(BOLD + BLUE + "👨‍🏫 PROFESSOR DASHBOARD" + RESET);
        System.out.println(BOLD + "=====================" + RESET);
        System.out.println("1. View My Courses");
        System.out.println("2. Grade Students");
        System.out.println("3. View Notifications");
        System.out.println("4. Chatbot Assistant");
        System.out.println("5. Send Message");
        System.out.println("6. Export Data");
        System.out.println("0. Logout");
        int choice = getIntInput(" Select option: ");
        switch (choice) {
            case 1: viewMyCourses(); break;
            case 2: gradeStudents(); break;
            case 3: viewNotifications(); break;
            case 4: chatbotAssistant(); break;
            case 5: sendMessage(); break; // New messaging option
            case 6: exportDataMenu(); break; // New export option
            case 0: logout(); break;
            default: System.out.println(RED + "Invalid option!" + RESET); pressEnterToContinue();
        }
    }

    private static void showStudentMenu() {
        clearScreen();
        // Use GREEN for the student header (keeping it as is)
        System.out.println(BOLD + GREEN + "📚 STUDENT DASHBOARD" + RESET);
        System.out.println(BOLD + "===================" + RESET);
        System.out.println("1. View Available Courses");
        System.out.println("2. Enroll in Course");
        System.out.println("3. View My Enrollments");
        System.out.println("4. View Notifications");
        System.out.println("5. Chatbot Assistant");
        System.out.println("6. Send Message");
        System.out.println("7. Export Data");
        System.out.println("0. Logout");
        int choice = getIntInput(" Select option: ");
        switch (choice) {
            case 1: viewAvailableCourses(); break;
            case 2: enrollInCourse(); break;
            case 3: viewMyEnrollments(); break;
            case 4: viewNotifications(); break;
            case 5: chatbotAssistant(); break;
            case 6: sendMessage(); break; // New messaging option
            case 7: exportDataMenu(); break; // New export option
            case 0: logout(); break;
            default: System.out.println(RED + "Invalid option!" + RESET); pressEnterToContinue();
        }
    }

    // Admin functionalities
    private static void manageUsers() {
        clearScreen();
        // Use SOFTER_BLUE for section headers
        System.out.println(BOLD + SOFTER_BLUE + "👥 USER MANAGEMENT" + RESET);
        System.out.println(BOLD + "=================" + RESET);
        System.out.println("1. Add User");
        System.out.println("2. View All Users");
        System.out.println("3. Edit User");
        System.out.println("4. Delete User");
        int choice = getIntInput(" Select option: ");
        switch (choice) {
            case 1:
                addUser();
                break;
            case 2:
                viewAllUsers();
                break;
            case 3:
                editUser();
                break;
            case 4:
                deleteUser();
                break;
            default:
                System.out.println(RED + "Invalid option!" + RESET);
                pressEnterToContinue();
        }
    }

    private static void addUser() {
        clearScreen();
        // Use SOFTER_BLUE for section headers
        System.out.println(BOLD + SOFTER_BLUE + "➕ ADD NEW USER" + RESET);
        System.out.println(BOLD + "===============" + RESET);
        String username = getStringInput("Username: ");
        String password = getStringInput("Password: ");
        String hashedPassword = hashPassword(password); // Hash the password
        if (hashedPassword == null) {
            System.out.println(RED + "Error hashing password. User not added." + RESET);
            pressEnterToContinue();
            return;
        }
        String role = getValidatedRoleInput("Role (admin/professor/student): ");
        String name = getStringInput("Full Name: ");
        try {
            PreparedStatement stmt = connection.prepareStatement(
                    "INSERT INTO users (username, password, role, name) VALUES (?, ?, ?, ?)",
                    Statement.RETURN_GENERATED_KEYS);
            stmt.setString(1, username);
            stmt.setString(2, hashedPassword); // Store hashed password
            stmt.setString(3, role);
            stmt.setString(4, name);
            stmt.execute();
            ResultSet generatedKeys = stmt.getGeneratedKeys();
            if (generatedKeys.next()) {
                int userId = generatedKeys.getInt(1);
                System.out.println(GREEN + "✅ User added successfully! User ID: " + userId + RESET);
            }
        } catch (SQLException e) {
            if (e.getErrorCode() == 1062) { // MySQL error code for duplicate entry
                System.out.println(RED + "❌ Error: Username already exists!" + RESET);
            } else {
                System.err.println(RED + "Error adding user: " + e.getMessage() + RESET);
            }
        }
        pressEnterToContinue();
    }

    private static void viewAllUsers() {
        clearScreen();
        // Use SOFTER_BLUE for section headers
        System.out.println(BOLD + SOFTER_BLUE + "📋 ALL USERS" + RESET);
        System.out.println(BOLD + "===========" + RESET);
        String searchTerm = "";
        System.out.print("Search users (name/username/role) or press Enter to show all: ");
        searchTerm = scanner.nextLine().trim();
        try {
            String query = "SELECT * FROM users";
            if (!searchTerm.isEmpty()) {
                query += " WHERE name LIKE ? OR username LIKE ? OR role LIKE ?";
            }
            query += " ORDER BY role, name";
            PreparedStatement stmt = connection.prepareStatement(query);
            if (!searchTerm.isEmpty()) {
                String searchPattern = "%" + searchTerm + "%";
                stmt.setString(1, searchPattern);
                stmt.setString(2, searchPattern);
                stmt.setString(3, searchPattern);
            }
            ResultSet rs = stmt.executeQuery();
            System.out.printf("%-5s %-20s %-15s %-30s%n", "ID", "Username", "Role", "Name");
            System.out.println("------------------------------------------------------------");
            boolean found = false;
            while (rs.next()) {
                found = true;
                System.out.printf("%-5d %-20s %-15s %-30s%n",
                        rs.getInt("id"),
                        rs.getString("username"),
                        rs.getString("role"),
                        rs.getString("name"));
            }
            if (!found) {
                System.out.println("No users found matching the search term.");
            }
        } catch (SQLException e) {
            System.err.println(RED + "Error retrieving users: " + e.getMessage() + RESET);
        }
        pressEnterToContinue();
    }

    private static void editUser() {
        clearScreen();
        // Use SOFTER_BLUE for section headers
        System.out.println(BOLD + SOFTER_BLUE + "✏️ EDIT USER" + RESET);
        System.out.println(BOLD + "===========" + RESET);
        // First, display users to select one
        viewAllUsers(); // Reuse viewAllUsers for selection, but we need the ID
        System.out.println(); // Add a line break
        int userId = getIntInput("Enter user ID to edit: ");
        try {
            // Fetch current user details
            PreparedStatement fetchStmt = connection.prepareStatement("SELECT * FROM users WHERE id = ?");
            fetchStmt.setInt(1, userId);
            ResultSet rs = fetchStmt.executeQuery();
            if (rs.next()) {
                String currentUsername = rs.getString("username");
                String currentRole = rs.getString("role");
                String currentName = rs.getString("name");
                System.out.println("Editing user: " + currentName + " (ID: " + userId + ")");
                System.out.println("Leave field blank to keep current value.");
                System.out.print("New Username (current: " + currentUsername + "): ");
                String newUsernameInput = scanner.nextLine().trim();
                String newUsername = newUsernameInput.isEmpty() ? currentUsername : newUsernameInput;
                System.out.print("New Password (leave blank to keep current): ");
                String newPasswordInput = scanner.nextLine().trim();
                String newHashedPassword;
                if (newPasswordInput.isEmpty()) {
                    // Keep current password (fetch it from DB)
                    newHashedPassword = rs.getString("password");
                } else {
                    newHashedPassword = hashPassword(newPasswordInput);
                    if (newHashedPassword == null) {
                        System.out.println(RED + "Error hashing new password. Password not updated." + RESET);
                        newHashedPassword = rs.getString("password"); // Keep old one
                    }
                }
                System.out.print("New Role (current: " + currentRole + "): ");
                String newRoleInput = scanner.nextLine().trim();
                String newRole = newRoleInput.isEmpty() ? currentRole : getValidatedRoleInput("New Role (admin/professor/student): "); // Re-prompt if changed
                System.out.print("New Full Name (current: " + currentName + "): ");
                String newNameInput = scanner.nextLine().trim();
                String newName = newNameInput.isEmpty() ? currentName : newNameInput;
                // Update the user
                PreparedStatement updateStmt = connection.prepareStatement(
                        "UPDATE users SET username=?, password=?, role=?, name=? WHERE id=?");
                updateStmt.setString(1, newUsername);
                updateStmt.setString(2, newHashedPassword);
                updateStmt.setString(3, newRole);
                updateStmt.setString(4, newName);
                updateStmt.setInt(5, userId);
                int rowsAffected = updateStmt.executeUpdate();
                if (rowsAffected > 0) {
                    System.out.println(GREEN + "✅ User updated successfully!" + RESET);
                } else {
                    System.out.println(YELLOW + "⚠️ No changes were made or user not found." + RESET);
                }
            } else {
                System.out.println(RED + "❌ User with ID " + userId + " not found!" + RESET);
            }
        } catch (SQLException e) {
            if (e.getErrorCode() == 1062) { // MySQL error code for duplicate entry
                System.out.println(RED + "❌ Error: Username already exists!" + RESET);
            } else {
                System.err.println(RED + "Error editing user: " + e.getMessage() + RESET);
            }
        }
        pressEnterToContinue();
    }

    private static void deleteUser() {
        clearScreen();
        // Use SOFTER_BLUE for section headers
        System.out.println(BOLD + SOFTER_BLUE + "🗑️ DELETE USER" + RESET);
        System.out.println(BOLD + "=============" + RESET);
        // Show all users first (with search)
        viewAllUsers();
        System.out.println(); // Add a line break
        int userId = getIntInput("Enter user ID to delete: ");
        if (userId == currentUserId) {
            System.out.println(RED + "❌ You cannot delete your own account!" + RESET);
            pressEnterToContinue();
            return;
        }
        try {
            // Confirm deletion
            PreparedStatement getNameStmt = connection.prepareStatement("SELECT name, username FROM users WHERE id = ?");
            getNameStmt.setInt(1, userId);
            ResultSet nameRs = getNameStmt.executeQuery();
            String userName = "Unknown User";
            if (nameRs.next()) {
                userName = nameRs.getString("name") + " (" + nameRs.getString("username") + ")";
            }
            if (!getConfirmation("Are you sure you want to delete user " + userName + " (ID: " + userId + ")?")) {
                System.out.println(YELLOW + "Deletion cancelled." + RESET);
                pressEnterToContinue();
                return;
            }
            PreparedStatement deleteStmt = connection.prepareStatement("DELETE FROM users WHERE id=?");
            deleteStmt.setInt(1, userId);
            int rows = deleteStmt.executeUpdate();
            if (rows > 0) {
                System.out.println(GREEN + "✅ User deleted successfully!" + RESET);
            } else {
                System.out.println(RED + "❌ User not found!" + RESET);
            }
        } catch (SQLException e) {
            System.err.println(RED + "Error deleting user: " + e.getMessage() + RESET);
        }
        pressEnterToContinue();
    }

    private static void manageCourses() {
        clearScreen();
        // Use SOFTER_BLUE for section headers
        System.out.println(BOLD + SOFTER_BLUE + "📖 COURSE MANAGEMENT" + RESET);
        System.out.println(BOLD + "===================" + RESET);
        System.out.println("1. Add Course");
        System.out.println("2. View All Courses");
        System.out.println("3. Assign Professor");
        int choice = getIntInput(" Select option: ");
        switch (choice) {
            case 1:
                addCourse();
                break;
            case 2:
                viewAllCourses();
                break;
            case 3:
                assignProfessor();
                break;
            default:
                System.out.println(RED + "Invalid option!" + RESET);
                pressEnterToContinue();
        }
    }

    private static void addCourse() {
        clearScreen();
        // Use SOFTER_BLUE for section headers
        System.out.println(BOLD + SOFTER_BLUE + "➕ ADD NEW COURSE" + RESET);
        System.out.println(BOLD + "================" + RESET);
        String code = getStringInput("Course Code: ");
        String name = getStringInput("Course Name: ");
        int credits = getIntInput("Credits: ");
        try {
            PreparedStatement stmt = connection.prepareStatement(
                    "INSERT INTO courses (code, name, credits) VALUES (?, ?, ?)",
                    Statement.RETURN_GENERATED_KEYS);
            stmt.setString(1, code);
            stmt.setString(2, name);
            stmt.setInt(3, credits);
            stmt.execute();
            ResultSet generatedKeys = stmt.getGeneratedKeys();
            if (generatedKeys.next()) {
                int courseId = generatedKeys.getInt(1);
                System.out.println(GREEN + "✅ Course added successfully! Course ID: " + courseId + RESET);
            }
        } catch (SQLException e) {
            if (e.getErrorCode() == 1062) { // MySQL error code for duplicate entry
                System.out.println(RED + "❌ Error: Course code already exists!" + RESET);
            } else {
                System.err.println(RED + "Error adding course: " + e.getMessage() + RESET);
            }
        }
        pressEnterToContinue();
    }

    private static void viewAllCourses() {
        clearScreen();
        // Use SOFTER_BLUE for section headers
        System.out.println(BOLD + SOFTER_BLUE + "📋 ALL COURSES" + RESET);
        System.out.println(BOLD + "=============" + RESET);
        String searchTerm = "";
        System.out.print("Search courses (code/name) or press Enter to show all: ");
        searchTerm = scanner.nextLine().trim();
        try {
            String query = "SELECT c.*, u.name as professor FROM courses c LEFT JOIN users u ON c.professor_id = u.id";
            if (!searchTerm.isEmpty()) {
                query += " WHERE c.code LIKE ? OR c.name LIKE ?";
            }
            query += " ORDER BY c.code";
            PreparedStatement stmt = connection.prepareStatement(query);
            if (!searchTerm.isEmpty()) {
                String searchPattern = "%" + searchTerm + "%";
                stmt.setString(1, searchPattern);
                stmt.setString(2, searchPattern);
            }
            ResultSet rs = stmt.executeQuery();
            System.out.printf("%-5s %-10s %-30s %-10s %-25s%n", "ID", "Code", "Name", "Credits", "Professor");
            System.out.println("------------------------------------------------------------------------");
            boolean found = false;
            while (rs.next()) {
                found = true;
                System.out.printf("%-5d %-10s %-30s %-10d %-25s%n",
                        rs.getInt("id"),
                        rs.getString("code"),
                        rs.getString("name"),
                        rs.getInt("credits"),
                        rs.getString("professor") != null ? rs.getString("professor") : "Not assigned");
            }
            if (!found) {
                System.out.println("No courses found matching the search term.");
            }
        } catch (SQLException e) {
            System.err.println(RED + "Error retrieving courses: " + e.getMessage() + RESET);
        }
        pressEnterToContinue();
    }

    private static void assignProfessor() {
        clearScreen();
        // Use SOFTER_BLUE for section headers
        System.out.println(BOLD + SOFTER_BLUE + "👨‍🏫 ASSIGN PROFESSOR TO COURSE" + RESET);
        System.out.println(BOLD + "=============================" + RESET);
        // Show available courses (with search)
        viewAllCourses();
        System.out.println(); // Add a line break
        int courseId = getIntInput("Enter course ID: ");
        try {
            // Show available professors
            PreparedStatement profStmt = connection.prepareStatement("SELECT id, name FROM users WHERE role='professor' ORDER BY name");
            ResultSet profRs = profStmt.executeQuery();
            System.out.println(" Available Professors:");
            System.out.printf("%-5s %-30s%n", "ID", "Name");
            System.out.println("--------------------------------");
            boolean profFound = false;
            while (profRs.next()) {
                profFound = true;
                System.out.printf("%-5d %-30s%n",
                        profRs.getInt("id"),
                        profRs.getString("name"));
            }
            if (!profFound) {
                System.out.println("No professors found.");
                pressEnterToContinue();
                return;
            }
            int profId = getIntInput(" Enter professor ID: ");
            // Confirm assignment
            PreparedStatement getCourseStmt = connection.prepareStatement("SELECT name, code FROM courses WHERE id = ?");
            getCourseStmt.setInt(1, courseId);
            ResultSet courseRs = getCourseStmt.executeQuery();
            String courseInfo = "Unknown Course";
            if (courseRs.next()) {
                courseInfo = courseRs.getString("code") + " - " + courseRs.getString("name");
            }
            PreparedStatement getProfStmt = connection.prepareStatement("SELECT name FROM users WHERE id = ?");
            getProfStmt.setInt(1, profId);
            ResultSet profInfoRs = getProfStmt.executeQuery();
            String profInfo = "Unknown Professor";
            if (profInfoRs.next()) {
                profInfo = profInfoRs.getString("name");
            }
            if (!getConfirmation("Assign professor " + profInfo + " to course " + courseInfo + "?")) {
                System.out.println(YELLOW + "Assignment cancelled." + RESET);
                pressEnterToContinue();
                return;
            }
            PreparedStatement updateStmt = connection.prepareStatement("UPDATE courses SET professor_id=? WHERE id=?");
            updateStmt.setInt(1, profId);
            updateStmt.setInt(2, courseId);
            int rowsAffected = updateStmt.executeUpdate();
            if (rowsAffected > 0) {
                System.out.println(GREEN + "✅ Professor assigned successfully!" + RESET);
            } else {
                System.out.println(RED + "❌ Course or Professor not found!" + RESET);
            }
        } catch (SQLException e) {
            System.err.println(RED + "Error assigning professor: " + e.getMessage() + RESET);
        }
        pressEnterToContinue();
    }

    private static void viewAllEnrollments() {
        clearScreen();
        // Use SOFTER_BLUE for section headers
        System.out.println(BOLD + SOFTER_BLUE + "📋 ALL ENROLLMENTS" + RESET);
        System.out.println(BOLD + "=================" + RESET);
        try {
            Statement stmt = connection.createStatement();
            ResultSet rs = stmt.executeQuery(
                    "SELECT e.id as enrollment_id, s.name as student, c.name as course, e.grade " +
                            "FROM enrollments e " +
                            "JOIN users s ON e.student_id = s.id " +
                            "JOIN courses c ON e.course_id = c.id " +
                            "ORDER BY s.name, c.name");
            System.out.printf("%-15s %-25s %-25s %-10s%n", "Enrollment ID", "Student", "Course", "Grade");
            System.out.println("------------------------------------------------------------------------");
            while (rs.next()) {
                System.out.printf("%-15d %-25s %-25s %-10s%n",
                        rs.getInt("enrollment_id"),
                        rs.getString("student"),
                        rs.getString("course"),
                        rs.getString("grade") != null ? rs.getString("grade") : "Not graded");
            }
        } catch (SQLException e) {
            System.err.println(RED + "Error retrieving enrollments: " + e.getMessage() + RESET);
        }
        pressEnterToContinue();
    }

    private static void sendNotification() {
        clearScreen();
        // Use SOFTER_BLUE for section headers
        System.out.println(BOLD + SOFTER_BLUE + "📢 SEND NOTIFICATION" + RESET);
        System.out.println(BOLD + "===================" + RESET);
        System.out.println("1. To All Users");
        System.out.println("2. To Specific User");
        int choice = getIntInput(" Select option: ");
        String message = getStringInput("Enter message: ");
        try {
            if (choice == 1) {
                if (!getConfirmation("Send this message to ALL users?")) {
                    System.out.println(YELLOW + "Notification cancelled." + RESET);
                    pressEnterToContinue();
                    return;
                }
                PreparedStatement stmt = connection.prepareStatement("SELECT id, username, name FROM users ORDER BY name");
                ResultSet rs = stmt.executeQuery();
                System.out.println(GREEN + " Sending to all users:" + RESET);
                System.out.printf("%-5s %-20s %-30s%n", "ID", "Username", "Name");
                System.out.println("------------------------------------------------------------");
                int userCount = 0;
                while (rs.next()) {
                    userCount++;
                    System.out.printf("%-5d %-20s %-30s%n",
                            rs.getInt("id"),
                            rs.getString("username"),
                            rs.getString("name"));
                }
                System.out.println(GREEN + "Total users: " + userCount + RESET);
                PreparedStatement insertStmt = connection.prepareStatement(
                        "INSERT INTO notifications (user_id, message) SELECT id, ? FROM users");
                insertStmt.setString(1, message);
                int rowsInserted = insertStmt.executeUpdate();
                System.out.println(GREEN + "✅ Notification sent to " + rowsInserted + " users!" + RESET);
            } else if (choice == 2) {
                // Show all users (with search)
                viewAllUsers();
                System.out.println(); // Add a line break
                int userId = getIntInput("Enter user ID: ");
                PreparedStatement insertStmt = connection.prepareStatement(
                        "INSERT INTO notifications (user_id, message) VALUES (?, ?)");
                insertStmt.setInt(1, userId);
                insertStmt.setString(2, message);
                insertStmt.execute();
                System.out.println(GREEN + "✅ Notification sent successfully!" + RESET);
            } else {
                System.out.println(RED + "Invalid option!" + RESET);
            }
        } catch (SQLException e) {
            System.err.println(RED + "Error sending notification: " + e.getMessage() + RESET);
        }
        pressEnterToContinue();
    }

    // Professor functionalities
    private static void viewMyCourses() {
        clearScreen();
        // Use SOFTER_BLUE for section headers
        System.out.println(BOLD + SOFTER_BLUE + "📘 MY COURSES" + RESET);
        System.out.println(BOLD + "=============" + RESET);
        try {
            PreparedStatement stmt = connection.prepareStatement(
                    "SELECT c.* FROM courses c WHERE c.professor_id = ? ORDER BY c.code");
            stmt.setInt(1, currentUserId);
            ResultSet rs = stmt.executeQuery();
            System.out.printf("%-5s %-10s %-30s %-10s%n", "ID", "Code", "Name", "Credits");
            System.out.println("----------------------------------------");
            while (rs.next()) {
                System.out.printf("%-5d %-10s %-30s %-10d%n",
                        rs.getInt("id"),
                        rs.getString("code"),
                        rs.getString("name"),
                        rs.getInt("credits"));
            }
        } catch (SQLException e) {
            System.err.println(RED + "Error retrieving courses: " + e.getMessage() + RESET);
        }
        pressEnterToContinue();
    }

    private static void gradeStudents() {
        clearScreen();
        // Use SOFTER_BLUE for section headers
        System.out.println(BOLD + SOFTER_BLUE + "📝 GRADE STUDENTS" + RESET);
        System.out.println(BOLD + "=================" + RESET);
        try {
            PreparedStatement stmt = connection.prepareStatement(
                    "SELECT e.id as enrollment_id, s.name as student, c.name as course " +
                            "FROM enrollments e " +
                            "JOIN users s ON e.student_id = s.id " +
                            "JOIN courses c ON e.course_id = c.id " +
                            "WHERE c.professor_id = ? ORDER BY s.name, c.name");
            stmt.setInt(1, currentUserId);
            ResultSet rs = stmt.executeQuery();
            System.out.printf("%-15s %-25s %-25s%n", "Enrollment ID", "Student", "Course");
            System.out.println("------------------------------------------------");
            boolean found = false;
            while (rs.next()) {
                found = true;
                System.out.printf("%-15d %-25s %-25s%n",
                        rs.getInt("enrollment_id"),
                        rs.getString("student"),
                        rs.getString("course"));
            }
            if (!found) {
                System.out.println("No students enrolled in your courses.");
                pressEnterToContinue();
                return;
            }
            int enrollmentId = getIntInput(" Enter enrollment ID to grade: ");
            String grade = getValidatedGradeInput("Enter grade (A-F, e.g., A, B+, C-): ");
            PreparedStatement updateStmt = connection.prepareStatement(
                    "UPDATE enrollments SET grade=? WHERE id=?");
            updateStmt.setString(1, grade);
            updateStmt.setInt(2, enrollmentId);
            int rowsAffected = updateStmt.executeUpdate();
            if (rowsAffected > 0) {
                System.out.println(GREEN + "✅ Grade submitted successfully!" + RESET);
            } else {
                System.out.println(RED + "❌ Enrollment ID not found or not associated with your course!" + RESET);
            }
        } catch (SQLException e) {
            System.err.println(RED + "Error grading student: " + e.getMessage() + RESET);
        }
        pressEnterToContinue();
    }

    // Student functionalities
    private static void viewAvailableCourses() {
        clearScreen();
        // Use SOFTER_BLUE for section headers
        System.out.println(BOLD + SOFTER_BLUE + "🔍 AVAILABLE COURSES" + RESET);
        System.out.println(BOLD + "===================" + RESET);
        String searchTerm = "";
        System.out.print("Search courses (code/name) or press Enter to show all: ");
        searchTerm = scanner.nextLine().trim();
        try {
            String query = "SELECT c.*, u.name as professor FROM courses c LEFT JOIN users u ON c.professor_id = u.id";
            if (!searchTerm.isEmpty()) {
                query += " WHERE c.code LIKE ? OR c.name LIKE ?";
            }
            query += " ORDER BY c.code";
            PreparedStatement stmt = connection.prepareStatement(query);
            if (!searchTerm.isEmpty()) {
                String searchPattern = "%" + searchTerm + "%";
                stmt.setString(1, searchPattern);
                stmt.setString(2, searchPattern);
            }
            ResultSet rs = stmt.executeQuery();
            System.out.printf("%-5s %-10s %-30s %-10s %-25s%n", "ID", "Code", "Name", "Credits", "Professor");
            System.out.println("------------------------------------------------------------------------");
            boolean found = false;
            while (rs.next()) {
                found = true;
                System.out.printf("%-5d %-10s %-30s %-10d %-25s%n",
                        rs.getInt("id"),
                        rs.getString("code"),
                        rs.getString("name"),
                        rs.getInt("credits"),
                        rs.getString("professor") != null ? rs.getString("professor") : "Not assigned");
            }
            if (!found) {
                System.out.println("No courses found matching the search term.");
            }
        } catch (SQLException e) {
            System.err.println(RED + "Error retrieving courses: " + e.getMessage() + RESET);
        }
        pressEnterToContinue();
    }

    private static void enrollInCourse() {
        clearScreen();
        // Use SOFTER_BLUE for section headers
        System.out.println(BOLD + SOFTER_BLUE + "📝 ENROLL IN COURSE" + RESET);
        System.out.println(BOLD + "==================" + RESET);
        // Show available courses (with search)
        viewAvailableCourses();
        System.out.println(); // Add a line break
        int courseId = getIntInput("Enter course ID to enroll: ");
        try {
            // Check if already enrolled
            PreparedStatement checkStmt = connection.prepareStatement(
                    "SELECT * FROM enrollments WHERE student_id = ? AND course_id = ?");
            checkStmt.setInt(1, currentUserId);
            checkStmt.setInt(2, courseId);
            ResultSet checkRs = checkStmt.executeQuery();
            if (checkRs.next()) {
                System.out.println(YELLOW + "⚠️ You are already enrolled in this course!" + RESET);
                pressEnterToContinue();
                return;
            }
            // Confirm enrollment
            PreparedStatement getCourseStmt = connection.prepareStatement("SELECT name, code FROM courses WHERE id = ?");
            getCourseStmt.setInt(1, courseId);
            ResultSet courseRs = getCourseStmt.executeQuery();
            String courseInfo = "Unknown Course";
            if (courseRs.next()) {
                courseInfo = courseRs.getString("code") + " - " + courseRs.getString("name");
            }
            if (!getConfirmation("Enroll in course " + courseInfo + "?")) {
                System.out.println(YELLOW + "Enrollment cancelled." + RESET);
                pressEnterToContinue();
                return;
            }
            PreparedStatement enrollStmt = connection.prepareStatement(
                    "INSERT INTO enrollments (student_id, course_id) VALUES (?, ?)");
            enrollStmt.setInt(1, currentUserId);
            enrollStmt.setInt(2, courseId);
            enrollStmt.execute();
            System.out.println(GREEN + "✅ Successfully enrolled in course!" + RESET);
        } catch (SQLException e) {
            System.err.println(RED + "Error enrolling in course: " + e.getMessage() + RESET);
        }
        pressEnterToContinue();
    }

    private static void viewMyEnrollments() {
        clearScreen();
        // Use SOFTER_BLUE for section headers
        System.out.println(BOLD + SOFTER_BLUE + "📘 MY ENROLLMENTS" + RESET);
        System.out.println(BOLD + "================" + RESET);
        try {
            PreparedStatement stmt = connection.prepareStatement(
                    "SELECT c.name as course, c.credits, e.grade " +
                            "FROM enrollments e " +
                            "JOIN courses c ON e.course_id = c.id " +
                            "WHERE e.student_id = ? ORDER BY c.name");
            stmt.setInt(1, currentUserId);
            ResultSet rs = stmt.executeQuery();
            System.out.printf("%-35s %-10s %-10s%n", "Course", "Credits", "Grade");
            System.out.println("----------------------------------------");
            while (rs.next()) {
                System.out.printf("%-35s %-10d %-10s%n",
                        rs.getString("course"),
                        rs.getInt("credits"),
                        rs.getString("grade") != null ? rs.getString("grade") : "Not graded");
            }
        } catch (SQLException e) {
            System.err.println(RED + "Error retrieving enrollments: " + e.getMessage() + RESET);
        }
        pressEnterToContinue();
    }

    // Notification system
    private static void viewNotifications() {
        clearScreen();
        // Use SOFTER_BLUE for section headers
        System.out.println(BOLD + SOFTER_BLUE + "🔔 NOTIFICATIONS" + RESET);
        System.out.println(BOLD + "================" + RESET);
        try {
            PreparedStatement stmt = connection.prepareStatement(
                    "SELECT * FROM notifications WHERE user_id = ? ORDER BY timestamp DESC LIMIT 20"); // Limit for readability
            stmt.setInt(1, currentUserId);
            ResultSet rs = stmt.executeQuery();
            MyArrayList<Integer> notificationIds = new MyArrayList<>();
            boolean hasNotifications = false;
            System.out.printf("%-5s %-25s %-50s %-10s%n", "ID", "Timestamp", "Message", "Status");
            System.out.println("------------------------------------------------------------------------");
            while (rs.next()) {
                hasNotifications = true;
                int id = rs.getInt("id");
                notificationIds.add(id);
                String timestamp = rs.getTimestamp("timestamp").toString();
                String message = rs.getString("message");
                String status = rs.getBoolean("is_read") ? "Read" : "NEW";
                System.out.printf("%-5d %-25s %-50s %-10s%n", id, timestamp, message.length() > 50 ? message.substring(0, 47) + "..." : message, status);
            }
            if (!hasNotifications) {
                System.out.println("No notifications found.");
                pressEnterToContinue();
                return;
            }
            System.out.println(" Options:");
            System.out.println("1. Mark specific notification as read");
            System.out.println("2. Mark all notifications as read");
            System.out.println("3. Delete a notification");
            System.out.println("0. Back to menu");
            int choice = getIntInput("Choose an option: ");
            switch (choice) {
                case 1:
                    int notifId = getIntInput("Enter notification ID to mark as read: ");
                    if (notificationIds.contains(notifId)) {
                        PreparedStatement updateStmt = connection.prepareStatement(
                                "UPDATE notifications SET is_read = TRUE WHERE id = ?");
                        updateStmt.setInt(1, notifId);
                        updateStmt.execute();
                        System.out.println(GREEN + "Notification marked as read." + RESET);
                    } else {
                        System.out.println(RED + "Invalid notification ID!" + RESET);
                    }
                    break;
                case 2:
                    PreparedStatement updateAllStmt = connection.prepareStatement(
                            "UPDATE notifications SET is_read = TRUE WHERE user_id = ?");
                    updateAllStmt.setInt(1, currentUserId);
                    updateAllStmt.execute();
                    System.out.println(GREEN + "All notifications marked as read." + RESET);
                    break;
                case 3:
                    int deleteId = getIntInput("Enter notification ID to delete: ");
                    if (notificationIds.contains(deleteId)) {
                        if (getConfirmation("Are you sure you want to delete this notification?")) {
                            PreparedStatement deleteStmt = connection.prepareStatement(
                                    "DELETE FROM notifications WHERE id = ?");
                            deleteStmt.setInt(1, deleteId);
                            deleteStmt.execute();
                            System.out.println(GREEN + "Notification deleted." + RESET);
                        } else {
                            System.out.println(YELLOW + "Deletion cancelled." + RESET);
                        }
                    } else {
                        System.out.println(RED + "Invalid notification ID!" + RESET);
                    }
                    break;
                case 0:
                    // Do nothing, just return
                    break;
                default:
                    System.out.println(RED + "Invalid option!" + RESET);
            }
        } catch (SQLException e) {
            System.err.println(RED + "Error retrieving notifications: " + e.getMessage() + RESET);
        }
        pressEnterToContinue();
    }

    // Messaging system
    private static void sendMessage() {
        clearScreen();
        // Use SOFTER_BLUE for section headers
        System.out.println(BOLD + SOFTER_BLUE + "✉️ SEND MESSAGE" + RESET);
        System.out.println(BOLD + "==============" + RESET);
        // Show all users (with search) to select recipient
        viewAllUsers();
        System.out.println(); // Add a line break
        int receiverId = getIntInput("Enter recipient's user ID: ");
        if (receiverId == currentUserId) {
            System.out.println(YELLOW + "⚠️ You cannot send a message to yourself!" + RESET);
            pressEnterToContinue();
            return;
        }
        // Check if recipient exists
        try {
            PreparedStatement checkStmt = connection.prepareStatement("SELECT name FROM users WHERE id = ?");
            checkStmt.setInt(1, receiverId);
            ResultSet checkRs = checkStmt.executeQuery();
            if (!checkRs.next()) {
                System.out.println(RED + "❌ Recipient user ID not found!" + RESET);
                pressEnterToContinue();
                return;
            }
            String recipientName = checkRs.getString("name");
            String subject = getStringInput("Subject: ");
            System.out.print("Content (end with a line containing only '.'): ");
            StringBuilder contentBuilder = new StringBuilder();
            String line;
            while (!(line = scanner.nextLine()).equals(".")) {
                contentBuilder.append(line).append(" ");
            }
            String content = contentBuilder.toString().trim();
            if (content.isEmpty()) {
                System.out.println(YELLOW + "⚠️ Message content is empty. Message not sent." + RESET);
                pressEnterToContinue();
                return;
            }
            // Confirm sending
            if (!getConfirmation("Send message to " + recipientName + "?")) {
                System.out.println(YELLOW + "Message sending cancelled." + RESET);
                pressEnterToContinue();
                return;
            }
            PreparedStatement insertStmt = connection.prepareStatement(
                    "INSERT INTO messages (sender_id, receiver_id, subject, content) VALUES (?, ?, ?, ?)");
            insertStmt.setInt(1, currentUserId);
            insertStmt.setInt(2, receiverId);
            insertStmt.setString(3, subject);
            insertStmt.setString(4, content);
            insertStmt.execute();
            System.out.println(GREEN + "✅ Message sent successfully to " + recipientName + "!" + RESET);
        } catch (SQLException e) {
            System.err.println(RED + "Error sending message: " + e.getMessage() + RESET);
        }
        pressEnterToContinue();
    }

    // Export Data Menu
    private static void exportDataMenu() {
        clearScreen();
        // Use SOFTER_BLUE for section headers
        System.out.println(BOLD + SOFTER_BLUE + "📤 EXPORT DATA" + RESET);
        System.out.println(BOLD + "=============" + RESET);
        System.out.println("1. Export User List");
        System.out.println("2. Export Course List");
        System.out.println("3. Export Enrollments List");
        System.out.println("0. Back to menu");
        int choice = getIntInput(" Select option: ");
        switch (choice) {
            case 1:
                exportUsers();
                break;
            case 2:
                exportCourses();
                break;
            case 3:
                exportEnrollments();
                break;
            case 0:
                // Return to menu
                break;
            default:
                System.out.println(RED + "Invalid option!" + RESET);
                pressEnterToContinue();
        }
    }

    // Export Users to TXT
    private static void exportUsers() {
        try {
            PreparedStatement stmt = connection.prepareStatement("SELECT * FROM users ORDER BY role, name");
            ResultSet rs = stmt.executeQuery();
            StringBuilder exportData = new StringBuilder();
            exportData.append("User List Export ");
                    exportData.append("================ ");
                            exportData.append(String.format("%-5s %-20s %-15s %-30s%n", "ID", "Username", "Role", "Name"));
            exportData.append("------------------------------------------------------------ ");
            while (rs.next()) {
                exportData.append(String.format("%-5d %-20s %-15s %-30s%n",
                        rs.getInt("id"),
                        rs.getString("username"),
                        rs.getString("role"),
                        rs.getString("name")));
            }
            String exportPath = "exports/" + currentUserRole + "/";
            createExportDirectory(exportPath);
            String fileName = "users_export_" + System.currentTimeMillis() + ".txt";
            String fullPath = exportPath + fileName;
            if (writeToFile(fullPath, exportData.toString())) {
                System.out.println(GREEN + "✅ User list exported successfully to: " + fullPath + RESET);
            } else {
                System.out.println(RED + "❌ Failed to export user list." + RESET);
            }
        } catch (SQLException e) {
            System.err.println(RED + "Error exporting users: " + e.getMessage() + RESET);
        }
        pressEnterToContinue();
    }

    // Export Courses to TXT
    private static void exportCourses() {
        try {
            PreparedStatement stmt = connection.prepareStatement(
                    "SELECT c.*, u.name as professor FROM courses c LEFT JOIN users u ON c.professor_id = u.id ORDER BY c.code");
            ResultSet rs = stmt.executeQuery();
            StringBuilder exportData = new StringBuilder();
            exportData.append("Course List Export ");
                    exportData.append("================= ");
                            exportData.append(String.format("%-5s %-10s %-30s %-10s %-25s%n", "ID", "Code", "Name", "Credits", "Professor"));
            exportData.append("------------------------------------------------------------------------ ");
            while (rs.next()) {
                exportData.append(String.format("%-5d %-10s %-30s %-10d %-25s%n",
                        rs.getInt("id"),
                        rs.getString("code"),
                        rs.getString("name"),
                        rs.getInt("credits"),
                        rs.getString("professor") != null ? rs.getString("professor") : "Not assigned"));
            }
            String exportPath = "exports/" + currentUserRole + "/";
            createExportDirectory(exportPath);
            String fileName = "courses_export_" + System.currentTimeMillis() + ".txt";
            String fullPath = exportPath + fileName;
            if (writeToFile(fullPath, exportData.toString())) {
                System.out.println(GREEN + "✅ Course list exported successfully to: " + fullPath + RESET);
            } else {
                System.out.println(RED + "❌ Failed to export course list." + RESET);
            }
        } catch (SQLException e) {
            System.err.println(RED + "Error exporting courses: " + e.getMessage() + RESET);
        }
        pressEnterToContinue();
    }

    // Export Enrollments to TXT
    private static void exportEnrollments() {
        try {
            String query;
            if ("admin".equals(currentUserRole)) {
                query = "SELECT e.id as enrollment_id, s.name as student, c.name as course, e.grade " +
                        "FROM enrollments e " +
                        "JOIN users s ON e.student_id = s.id " +
                        "JOIN courses c ON e.course_id = c.id " +
                        "ORDER BY s.name, c.name";
            } else if ("professor".equals(currentUserRole)) {
                query = "SELECT e.id as enrollment_id, s.name as student, c.name as course, e.grade " +
                        "FROM enrollments e " +
                        "JOIN users s ON e.student_id = s.id " +
                        "JOIN courses c ON e.course_id = c.id " +
                        "WHERE c.professor_id = ? " +
                        "ORDER BY s.name, c.name";
            } else { // student
                query = "SELECT c.name as course, c.credits, e.grade " +
                        "FROM enrollments e " +
                        "JOIN courses c ON e.course_id = c.id " +
                        "WHERE e.student_id = ? " +
                        "ORDER BY c.name";
            }
            PreparedStatement stmt = connection.prepareStatement(query);
            if (!"admin".equals(currentUserRole)) {
                stmt.setInt(1, currentUserId);
            }
            ResultSet rs = stmt.executeQuery();
            StringBuilder exportData = new StringBuilder();
            exportData.append("Enrollment List Export ");
                    exportData.append("===================== ");
            if ("admin".equals(currentUserRole) || "professor".equals(currentUserRole)) {
                exportData.append(String.format("%-15s %-25s %-25s %-10s%n", "Enrollment ID", "Student", "Course", "Grade"));
                exportData.append("------------------------------------------------------------------------ ");
            } else {
                exportData.append(String.format("%-35s %-10s %-10s%n", "Course", "Credits", "Grade"));
                exportData.append("---------------------------------------- ");
            }
            boolean found = false;
            while (rs.next()) {
                found = true;
                if ("admin".equals(currentUserRole) || "professor".equals(currentUserRole)) {
                    exportData.append(String.format("%-15d %-25s %-25s %-10s%n",
                            rs.getInt("enrollment_id"),
                            rs.getString("student"),
                            rs.getString("course"),
                            rs.getString("grade") != null ? rs.getString("grade") : "Not graded"));
                } else {
                    exportData.append(String.format("%-35s %-10d %-10s%n",
                            rs.getString("course"),
                            rs.getInt("credits"),
                            rs.getString("grade") != null ? rs.getString("grade") : "Not graded"));
                }
            }
            if (!found) {
                exportData.append("No enrollments found. ");
            }
            String exportPath = "exports/" + currentUserRole + "/";
            createExportDirectory(exportPath);
            String fileName = "enrollments_export_" + System.currentTimeMillis() + ".txt";
            String fullPath = exportPath + fileName;
            if (writeToFile(fullPath, exportData.toString())) {
                System.out.println(GREEN + "✅ Enrollment list exported successfully to: " + fullPath + RESET);
            } else {
                System.out.println(RED + "❌ Failed to export enrollment list." + RESET);
            }
        } catch (SQLException e) {
            System.err.println(RED + "Error exporting enrollments: " + e.getMessage() + RESET);
        }
        pressEnterToContinue();
    }

    // Chatbot assistant - Enhanced with procedural commands and smart queries
    private static void chatbotAssistant() {
        clearScreen();
        System.out.println(BOLD + SOFTER_BLUE + "🤖 UNIVERSITY CHATBOT" + RESET); // Use SOFTER_BLUE
        System.out.println(BOLD + "====================" + RESET);
        System.out.println("Ask me anything about the university system!");
        System.out.println("Type '" + BOLD + "'exit'" + RESET + "' to return to main menu.");
        System.out.println("Examples: 'Enroll me in CS101', 'Show my grades', 'What courses does Professor Davis teach?', 'Show grade distribution for MATH202'");

        String lastContextCourseCode = null; // Simple context for follow-ups

        while (true) {
            System.out.print(" You: ");
            String input = scanner.nextLine().toLowerCase().trim();
            if (input.equals("exit")) {
                break;
            }
            System.out.print(BOLD + "Bot: " + RESET);

            // --- Procedural Command Execution ---

            // 1. Enroll me in course CS101
            if (input.matches("enroll me in [a-z0-9]+")) {
                if (!"student".equals(currentUserRole)) {
                    System.out.println("Only students can enroll in courses.");
                } else {
                    String courseCode = input.substring(input.lastIndexOf(' ') + 1).toUpperCase();
                    handleEnrollCommand(courseCode);
                    lastContextCourseCode = courseCode; // Set context
                }
                continue; // Skip general response
            }

            // 3. List students with grades below C in CS101
            if (input.contains("list students with grades below") && input.contains("in")) {
                // Simple parsing: assume format like "list students with grades below C in CS101"
                String[] parts = input.split(" ");
                String gradeThreshold = "C"; // Default or parse from input
                String courseCode = null;
                for (int i = 0; i < parts.length; i++) {
                    if ("in".equals(parts[i]) && i + 1 < parts.length) {
                        courseCode = parts[i + 1].toUpperCase();
                        break;
                    }
                }
                if (courseCode != null) {
                    handleListStudentsBelowGradeCommand(courseCode, gradeThreshold);
                    lastContextCourseCode = courseCode; // Set context
                } else {
                    System.out.println("Could not identify the course. Please specify like '... in CS101'.");
                }
                continue;
            }

            // 4. What courses does Dr. Smith teach?
            if (input.contains("what courses does") && input.contains("teach")) {
                // Simple parsing: assume format like "what courses does Professor Name teach"
                String potentialName = input.replace("what courses does", "").replace("teach", "").trim();
                if (!potentialName.isEmpty()) {
                    // Try to find professor by name (exact match for simplicity)
                    handleCoursesByProfessorCommand(potentialName);
                } else {
                    System.out.println("Could not identify the professor's name.");
                }
                continue;
            }

            // 5. Show grade distribution for CS101
            if (input.contains("show grade distribution for")) {
                String courseCode = input.substring(input.lastIndexOf(' ') + 1).toUpperCase();
                handleGradeDistributionCommand(courseCode);
                lastContextCourseCode = courseCode; // Set context
                continue;
            }

            // 6. Assign Professor Johnson to course MATH202 (Admin only)
            if (input.contains("assign") && input.contains("to course") && "admin".equals(currentUserRole)) {
                // Simple parsing: "assign Professor Name to course CODE"
                int profStart = input.indexOf("assign") + 7; // After "assign "
                int profEnd = input.indexOf(" to course");
                int courseStart = input.indexOf(" to course") + 11; // After " to course "

                if (profStart > 6 && profEnd > profStart && courseStart > (profEnd + 11)) {
                    String professorName = input.substring(profStart, profEnd).trim();
                    String courseCode = input.substring(courseStart).trim().toUpperCase();
                    handleAssignProfessorCommand(professorName, courseCode);
                    lastContextCourseCode = courseCode; // Set context
                } else {
                    System.out.println("Could not parse the command. Please use format: 'Assign Professor Name to course CODE'");
                }
                continue;
            }


            // --- Smart Queries & Information Retrieval ---

            // 2. Show my current grades (Refined)
            if (input.contains("show") && input.contains("grade")) {
                handleShowMyGradesCommand();
                continue; // Skip general response
            }

            // Improved "my course/class" query
            if (input.contains("my") && (input.contains("course") || input.contains("class"))) {
                // This logic is already present, but we can refine the output slightly
                if ("student".equals(currentUserRole)) {
                    System.out.println("Let me fetch your enrolled courses...");
                    try {
                        PreparedStatement stmt = connection.prepareStatement(
                                "SELECT c.name as course, c.code FROM enrollments e JOIN courses c ON e.course_id = c.id WHERE e.student_id = ? ORDER BY c.name");
                        stmt.setInt(1, currentUserId);
                        ResultSet rs = stmt.executeQuery();
                        System.out.print("Your courses: ");
                        boolean found = false;
                        while (rs.next()) {
                            if (found) System.out.print(", ");
                            System.out.print(rs.getString("code") + " - " + rs.getString("course"));
                            found = true;
                        }
                        if (!found) {
                            System.out.print("You are not enrolled in any courses.");
                        }
                        System.out.println();
                    } catch (SQLException e) {
                        System.out.println("Sorry, I couldn't fetch your courses right now.");
                    }
                } else if ("professor".equals(currentUserRole)) {
                    System.out.println("Let me fetch your courses...");
                    try {
                        PreparedStatement stmt = connection.prepareStatement(
                                "SELECT c.name as course, c.code FROM courses c WHERE c.professor_id = ? ORDER BY c.name");
                        stmt.setInt(1, currentUserId);
                        ResultSet rs = stmt.executeQuery();
                        System.out.print("Your courses: ");
                        boolean found = false;
                        while (rs.next()) {
                            if (found) System.out.print(", ");
                            System.out.print(rs.getString("code") + " - " + rs.getString("course"));
                            found = true;
                        }
                        if (!found) {
                            System.out.print("You are not assigned to any courses.");
                        }
                        System.out.println();
                    } catch (SQLException e) {
                        System.out.println("Sorry, I couldn't fetch your courses right now.");
                    }
                } else {
                    System.out.println("Your role does not have specific courses assigned in this way.");
                }
                continue; // Skip general response
            }

            // --- General/Existing Responses ---
            if (input.contains("hello") || input.contains("hi")) {
                System.out.println("Hello! How can I assist you today?");
            } else if (input.contains("course")) {
                System.out.println("You can view available courses in the course menu. " +
                        "Students can enroll in courses, and professors can manage their courses.");
            } else if (input.contains("notification") || input.contains("message")) {
                System.out.println("You can view your notifications in the notifications section. " +
                        "Administrators can send messages to users.");
            } else if (input.contains("user") || input.contains("account")) {
                System.out.println("Administrators can manage user accounts. " +
                        "Each user has a specific role: admin, professor, or student.");
            } else if (input.contains("help")) {
                System.out.println("Available commands:");
                System.out.println("- Courses: 'View available courses', 'Enroll me in CS101'");
                System.out.println("- Grades: 'Show my grades', 'Show grade distribution for CS101'");
                System.out.println("- Users/Courses: 'What courses does Professor Smith teach?', 'List students with grades below C in CS101'");
                if ("admin".equals(currentUserRole)) {
                    System.out.println("- Admin: 'Assign Professor Name to course CODE'");
                }
                System.out.println("- General: 'My courses', 'Notifications', 'Users', 'Help', 'Exit'");
            } else if (input.contains("export")) {
                System.out.println("You can export data (user lists, course lists, enrollments) from the Export Data menu in your dashboard.");
            } else {
                // Intent Clarification for ambiguous inputs
                if (input.contains("enroll")) {
                    System.out.println("Did you mean 'Enroll me in [Course Code]'?");
                } else if (input.contains("grade") || input.contains("score")) {
                    System.out.println("Did you mean 'Show my grades' or 'Show grade distribution for [Course Code]'?");
                } else if (input.contains("professor") && input.contains("course")) {
                    System.out.println("Did you mean 'What courses does [Professor Name] teach?' or 'Assign [Professor Name] to course [Code]' (Admin)?");
                } else {
                    System.out.println("I'm not sure about that. Try asking about courses, grades, notifications, or export. Type 'help' for suggestions.");
                }
            }
        }
    }

    // --- Helper Methods for Chatbot Commands ---

    private static void handleEnrollCommand(String courseCode) {
        try {
            // 1. Find Course ID by Code
            PreparedStatement courseStmt = connection.prepareStatement("SELECT id FROM courses WHERE code = ?");
            courseStmt.setString(1, courseCode);
            ResultSet courseRs = courseStmt.executeQuery();
            if (!courseRs.next()) {
                System.out.println("❌ Course " + courseCode + " not found.");
                return;
            }
            int courseId = courseRs.getInt("id");

            // 2. Check if already enrolled
            PreparedStatement checkStmt = connection.prepareStatement(
                    "SELECT * FROM enrollments WHERE student_id = ? AND course_id = ?");
            checkStmt.setInt(1, currentUserId);
            checkStmt.setInt(2, courseId);
            ResultSet checkRs = checkStmt.executeQuery();
            if (checkRs.next()) {
                System.out.println("⚠️ You are already enrolled in " + courseCode + ".");
                return;
            }

            // 3. Enroll
            PreparedStatement enrollStmt = connection.prepareStatement(
                    "INSERT INTO enrollments (student_id, course_id) VALUES (?, ?)");
            enrollStmt.setInt(1, currentUserId);
            enrollStmt.setInt(2, courseId);
            enrollStmt.execute();
            System.out.println("✅ Successfully enrolled you in " + courseCode + "!");
        } catch (SQLException e) {
            System.err.println(RED + "Error processing enrollment: " + e.getMessage() + RESET);
            System.out.println("Sorry, I couldn't process your enrollment request.");
        }
    }

    private static void handleShowMyGradesCommand() {
        System.out.println("Let me fetch your grades...");
        try {
            PreparedStatement stmt = connection.prepareStatement(
                    "SELECT c.code, c.name, e.grade " +
                            "FROM enrollments e " +
                            "JOIN courses c ON e.course_id = c.id " +
                            "WHERE e.student_id = ? " +
                            "ORDER BY c.code");
            stmt.setInt(1, currentUserId);
            ResultSet rs = stmt.executeQuery();
            System.out.println("Your Grades:");
            System.out.printf("%-10s %-30s %-10s%n", "Code", "Course Name", "Grade");
            System.out.println("----------------------------------------------");
            boolean found = false;
            while (rs.next()) {
                found = true;
                System.out.printf("%-10s %-30s %-10s%n",
                        rs.getString("code"),
                        rs.getString("name"),
                        rs.getString("grade") != null ? rs.getString("grade") : "Not graded");
            }
            if (!found) {
                System.out.println("You are not enrolled in any courses or have no grades yet.");
            }
        } catch (SQLException e) {
            System.err.println(RED + "Error fetching grades: " + e.getMessage() + RESET);
            System.out.println("Sorry, I couldn't fetch your grades right now.");
        }
    }

    private static void handleListStudentsBelowGradeCommand(String courseCode, String gradeThreshold) {
        // Define a simple grade order for comparison (A+ is highest, F is lowest)
        // This is a basic mapping. A full system might use a more robust comparison.
//        java.util.Map<String, Integer> gradeOrder = new java.util.HashMap<>();
//        String[] grades = {"F", "D-", "D", "D+", "C-", "C", "C+", "B-", "B", "B+", "A-", "A", "A+"};
//        for (int i = 0; i < grades.length; i++) {
//            gradeOrder.put(grades[i], i);
//        }
//        int thresholdIndex = gradeOrder.getOrDefault(gradeThreshold, gradeOrder.get("C")); // Default to C if not found

        MyArrayList<String> g = new MyArrayList<>();
        MyArrayList<Integer> v = new MyArrayList<>();
        String[] grades = {"F","D-","D","D+","C-","C","C+","B-","B","B+","A-","A","A+"};
        for (int i = 0; i < grades.length; i++) { g.add(grades[i]); v.add(i); }
        int thresholdIndex = gradeIndex(gradeThreshold, g, v);
        if (thresholdIndex == -1) thresholdIndex = gradeIndex("C", g, v);

        try {
            PreparedStatement courseStmt = connection.prepareStatement("SELECT id FROM courses WHERE code = ?");
            courseStmt.setString(1, courseCode);
            ResultSet courseRs = courseStmt.executeQuery();
            if (!courseRs.next()) {
                System.out.println("❌ Course " + courseCode + " not found.");
                return;
            }
            int courseId = courseRs.getInt("id");

            PreparedStatement stmt = connection.prepareStatement(
                    "SELECT u.name, e.grade " +
                            "FROM enrollments e " +
                            "JOIN users u ON e.student_id = u.id " +
                            "WHERE e.course_id = ? AND e.grade IS NOT NULL");
            stmt.setInt(1, courseId);
            ResultSet rs = stmt.executeQuery();

            System.out.println("Students with grades below " + gradeThreshold + " in " + courseCode + ":");
            System.out.printf("%-30s %-10s%n", "Student Name", "Grade");
            System.out.println("----------------------------------------");
            boolean found = false;
            while (rs.next()) {
                String studentGrade = rs.getString("grade");
                int studentGradeIndex = gradeOrder.getOrDefault(studentGrade, -1);
                if (studentGradeIndex >= 0 && studentGradeIndex < thresholdIndex) {
                    found = true;
                    System.out.printf("%-30s %-10s%n", rs.getString("name"), studentGrade);
                }
            }
            if (!found) {
                System.out.println("No students found with grades below " + gradeThreshold + " in this course.");
            }
        } catch (SQLException e) {
            System.err.println(RED + "Error fetching student list: " + e.getMessage() + RESET);
            System.out.println("Sorry, I couldn't fetch the student list right now.");
        }
    }

    private static void handleCoursesByProfessorCommand(String professorName) {
        try {
            // Try to find the professor by name (exact match for simplicity)
            PreparedStatement profStmt = connection.prepareStatement(
                    "SELECT id, name FROM users WHERE role = 'professor' AND (name LIKE ? OR username LIKE ?)");
            // Allow partial match on name or exact match on username
            profStmt.setString(1, "%" + professorName + "%");
            profStmt.setString(2, professorName);
            ResultSet profRs = profStmt.executeQuery();

            if (!profRs.next()) {
                System.out.println("❌ Professor '" + professorName + "' not found.");
                return;
            }
            int professorId = profRs.getInt("id");
            String foundProfessorName = profRs.getString("name");

            PreparedStatement courseStmt = connection.prepareStatement(
                    "SELECT code, name FROM courses WHERE professor_id = ? ORDER BY code");
            courseStmt.setInt(1, professorId);
            ResultSet courseRs = courseStmt.executeQuery();

            System.out.println(foundProfessorName + " teaches the following courses:");
            System.out.printf("%-10s %-30s%n", "Code", "Course Name");
            System.out.println("----------------------------------------");
            boolean found = false;
            while (courseRs.next()) {
                found = true;
                System.out.printf("%-10s %-30s%n",
                        courseRs.getString("code"),
                        courseRs.getString("name"));
            }
            if (!found) {
                System.out.println(foundProfessorName + " is not currently assigned to any courses.");
            }
        } catch (SQLException e) {
            System.err.println(RED + "Error fetching professor's courses: " + e.getMessage() + RESET);
            System.out.println("Sorry, I couldn't fetch the courses right now.");
        }
    }

    private static void handleGradeDistributionCommand(String courseCode) {
        try {
            PreparedStatement courseStmt = connection.prepareStatement("SELECT id, name FROM courses WHERE code = ?");
            courseStmt.setString(1, courseCode);
            ResultSet courseRs = courseStmt.executeQuery();
            if (!courseRs.next()) {
                System.out.println("❌ Course " + courseCode + " not found.");
                return;
            }
            int courseId = courseRs.getInt("id");
            String courseName = courseRs.getString("name");

            PreparedStatement stmt = connection.prepareStatement(
                    "SELECT grade, COUNT(*) as count FROM enrollments WHERE course_id = ? AND grade IS NOT NULL GROUP BY grade ORDER BY grade");
            stmt.setInt(1, courseId);
            ResultSet rs = stmt.executeQuery();

            System.out.println("Grade Distribution for " + courseCode + " - " + courseName + ":");
            System.out.printf("%-10s %-10s%n", "Grade", "Count");
            System.out.println("--------------------");
            boolean found = false;
            while (rs.next()) {
                found = true;
                System.out.printf("%-10s %-10d%n",
                        rs.getString("grade"),
                        rs.getInt("count"));
            }
            if (!found) {
                System.out.println("No grades have been recorded for this course yet.");
            }
        } catch (SQLException e) {
            System.err.println(RED + "Error fetching grade distribution: " + e.getMessage() + RESET);
            System.out.println("Sorry, I couldn't fetch the grade distribution right now.");
        }
    }

    private static void handleAssignProfessorCommand(String professorName, String courseCode) {
        try {
            // 1. Find Professor ID
            PreparedStatement profStmt = connection.prepareStatement(
                    "SELECT id, name FROM users WHERE role = 'professor' AND (name LIKE ? OR username LIKE ?)");
            profStmt.setString(1, "%" + professorName + "%");
            profStmt.setString(2, professorName);
            ResultSet profRs = profStmt.executeQuery();
            if (!profRs.next()) {
                System.out.println("❌ Professor '" + professorName + "' not found.");
                return;
            }
            int professorId = profRs.getInt("id");
            String foundProfessorName = profRs.getString("name");

            // 2. Find Course ID
            PreparedStatement courseStmt = connection.prepareStatement("SELECT id, name FROM courses WHERE code = ?");
            courseStmt.setString(1, courseCode);
            ResultSet courseRs = courseStmt.executeQuery();
            if (!courseRs.next()) {
                System.out.println("❌ Course " + courseCode + " not found.");
                return;
            }
            int courseId = courseRs.getInt("id");
            String foundCourseName = courseRs.getString("name");

            // 3. Confirm Assignment (Simple confirmation)
            System.out.println("Assigning " + foundProfessorName + " to " + courseCode + " - " + foundCourseName + "?");
            if (!getConfirmation("Confirm assignment?")) {
                System.out.println("Assignment cancelled.");
                return;
            }

            // 4. Assign Professor
            PreparedStatement updateStmt = connection.prepareStatement("UPDATE courses SET professor_id=? WHERE id=?");
            updateStmt.setInt(1, professorId);
            updateStmt.setInt(2, courseId);
            int rowsAffected = updateStmt.executeUpdate();
            if (rowsAffected > 0) {
                System.out.println(GREEN + "✅ Professor " + foundProfessorName + " assigned successfully to " + courseCode + "!" + RESET);
            } else {
                System.out.println(RED + "❌ Failed to assign professor. Course or Professor not found!" + RESET);
            }
        } catch (SQLException e) {
            System.err.println(RED + "Error assigning professor: " + e.getMessage() + RESET);
            System.out.println("Sorry, I couldn't process the assignment request.");
        }
    }

    // Ensure getConfirmation method is available (it should be in your current code)
    // private static boolean getConfirmation(String prompt) { ... }

    // Utility methods
    private static void logout() {
        currentUserId = -1;
        currentUserRole = "";
        System.out.println(GREEN + "👋 Logged out successfully!" + RESET);
        pressEnterToContinue();
    }

    private static void clearScreen() {
        System.out.print("\033[H\033[2J");
        System.out.flush();
    }

    private static void animateLoading() {
        String[] animation = {"⢿", "⣻", "⣽", "⣾", "⣷", "⣯", "⣟", "⡿"};
        // Use SOFTER_BLUE for the loading animation
        for (int i = 0; i < 20; i++) {
            System.out.print("\r" + SOFTER_BLUE + animation[i % animation.length] + " Loading..." + RESET);
            try {
                TimeUnit.MILLISECONDS.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        // Use GREEN for the ready message
        System.out.println("\r" + GREEN + "✅ System ready!           " + RESET);
    }

    // Deprecated old method, replaced by getIntInput(String prompt)
    // private static int getIntInput() {
    //     try {
    //         return Integer.parseInt(scanner.nextLine());
    //     } catch (NumberFormatException e) {
    //         return -1;
    //     }
    // }

    private static void pressEnterToContinue() {
        System.out.println(GREEN + " Press Enter to continue..." + RESET);
        scanner.nextLine();
    }

    private static int gradeIndex(String grade, MyArrayList<String> keys, MyArrayList<Integer> vals) {
        for (int i = 0; i < keys.size(); i++) if (keys.get(i).equals(grade)) return vals.get(i);
        return -1;
    }

    /* =========================================================
   CUSTOM DATA-STRUCTURE IMPLEMENTATIONS  (append to file)
   ========================================================= */
    /* Dynamic array (ArrayList replacement) */
    static class MyArrayList<T> {
        private Object[] arr;
        private int size;
        MyArrayList() { arr = new Object[10]; size = 0; }
        void add(T val) {
            if (size == arr.length) grow();
            arr[size++] = val;
        }
        T get(int idx) { return (T) arr[idx]; }
        int size() { return size; }
        boolean isEmpty() { return size == 0; }
        private void grow() {
            Object[] bigger = new Object[arr.length * 2];
            for (int i = 0; i < size; i++) bigger[i] = arr[i];
            arr = bigger;
        }
    }

    /* Singly linked list */
    static class MyLinkedList<T> {
        private class Node { T data; Node next; Node(T d) { data = d; } }
        private Node head; private int size;
        void add(T val) {
            Node n = new Node(val);
            if (head == null) { head = n; } else {
                Node cur = head;
                while (cur.next != null) cur = cur.next;
                cur.next = n;
            }
            size++;
        }
        T get(int idx) {
            Node cur = head;
            for (int i = 0; i < idx; i++) cur = cur.next;
            return cur.data;
        }
        int size() { return size; }
        boolean isEmpty() { return size == 0; }
    }

    /* Queue (FIFO) using linked nodes */
    static class MyQueue<T> {
        private class Node { T data; Node next; Node(T d) { data = d; } }
        private Node front, rear; private int size;
        void enqueue(T val) {
            Node n = new Node(val);
            if (rear == null) { front = rear = n; } else { rear.next = n; rear = n; }
            size++;
        }
        T dequeue() {
            if (front == null) return null;
            T val = front.data;
            front = front.next;
            if (front == null) rear = null;
            size--;
            return val;
        }
        T peek() { return front == null ? null : front.data; }
        boolean isEmpty() { return size == 0; }
    }

    /* Stack (LIFO) using linked nodes */
    static class MyStack<T> {
        private class Node { T data; Node next; Node(T d) { data = d; } }
        private Node top; private int size;
        void push(T val) { Node n = new Node(val); n.next = top; top = n; size++; }
        T pop() { if (top == null) return null; T val = top.data; top = top.next; size--; return val; }
        T peek() { return top == null ? null : top.data; }
        boolean isEmpty() { return size == 0; }
    }

    /* tiny helper for index-of */
    private static int indexOf(MyArrayList<Integer> list, int value) {
        for (int i = 0; i < list.size(); i++) if (list.get(i) == value) return i;
        return -1;
    }
}