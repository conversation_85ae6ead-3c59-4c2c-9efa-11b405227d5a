import java.io.*;
import java.nio.file.*;
import java.security.*;
import java.sql.*;
import java.text.*;
import java.util.*;
import java.util.regex.*;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;

public class UniOneShot {

    /* ---- UTF-8 on Windows ---- */
    static { try { new ProcessBuilder("cmd", "/c", "chcp 65001 > nul").inheritIO().start().waitFor(); } catch (Exception ignored) {} }

    /* ---- JDBC ---- */
    private static Connection conn() throws SQLException {
        return DriverManager.getConnection(
                "****************************************************************", "root", "");
    }

    /* ---- models ---- */
    private record Student(int id, String email, String name) {}
    private record Faculty(int id, String email, String name) {}
    private record Course(int id,String code,String title,int credits,boolean deleted) {}
    private record Section(int id,int courseId,String code,String title,int credits,String faculty,int capacity,int enrolled,boolean deleted) {}

    /* ---- caches ---- */
    private static final List<Course>  courses  = new ArrayList<>();
    private static final List<Section> sections = new ArrayList<>();
    private static final Scanner sc = new Scanner(System.in);

    /* ---- colours ---- */
    private static final String RESET  = "\u001B[0m";
    private static final String BOLD   = "\u001B[1m";
    private static final String GREEN  = "\u001B[32m";
    private static final String RED    = "\u001B[31m";
    private static final String CYAN   = "\u001B[36m";
    private static final String YELLOW = "\u001B[33m";
    private static final String PURPLE = "\u001B[35m";

    /* ---- helpers ---- */
    private static void pause() { System.out.print(GREEN + "\n✔ Press ENTER…"); sc.nextLine(); }
    private static int readInt() {
        while (true) try { return Integer.parseInt(sc.nextLine().trim()); }
        catch (Exception e) { System.out.print(RED + "Enter integer: "); }
    }
    private static String hash(String pwd) {
        try { byte[] salt = "uniSalt".getBytes();
            PBEKeySpec spec = new PBEKeySpec(pwd.toCharArray(), salt, 1000, 256);
            SecretKeyFactory skf = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256");
            return Base64.getEncoder().encodeToString(skf.generateSecret(spec).getEncoded());
        } catch (Exception e) { return pwd; }
    }
    private static void log(String actor, String action, String details) {
        try (Connection c = conn();
             PreparedStatement ps = c.prepareStatement(
                     "INSERT INTO audit_log(actor,action,details) VALUES(?,?,?)")) {
            ps.setString(1, actor); ps.setString(2, action); ps.setString(3, details); ps.executeUpdate();
        } catch (SQLException ignored) {}
    }
    private static void notify(String email, String msg) {
        try (Connection c = conn();
             PreparedStatement ps = c.prepareStatement("INSERT INTO notification(user_email,message) VALUES(?,?)")) {
            ps.setString(1, email); ps.setString(2, msg); ps.executeUpdate();
        } catch (SQLException ignored) {}
    }

    /* ---- loaders ---- */
    private static void loadData() {
        courses.clear(); sections.clear();
        try (Connection c = conn()) {
            try (Statement st = c.createStatement();
                 ResultSet rs = st.executeQuery("SELECT id,code,title,credits,deleted FROM course WHERE deleted=0")) {
                while (rs.next()) courses.add(new Course(rs.getInt(1), rs.getString(2), rs.getString(3), rs.getInt(4), rs.getBoolean(5)));
            }
            try (Statement st = c.createStatement();
                 ResultSet rs = st.executeQuery(
                         "SELECT s.id,s.course_id,c.code,c.title,c.credits,f.name,s.capacity,s.enrolled,s.deleted " +
                                 "FROM section s JOIN course c ON s.course_id=c.id JOIN faculty f ON s.faculty_id=f.id WHERE s.deleted=0")) {
                while (rs.next()) sections.add(new Section(rs.getInt(1), rs.getInt(2), rs.getString(3), rs.getString(4),
                        rs.getInt(5), rs.getString(6), rs.getInt(7), rs.getInt(8), rs.getBoolean(9)));
            }
        } catch (SQLException e) { System.out.println(RED + "DB load error"); }
    }

    /* ---- ASCII banner ---- */
    private static void banner() {
        System.out.println(
                CYAN + "███╗   ██╗███╗   ██╗██╗███████╗██╗  ██╗ ██████╗ ████████╗\n" +
                        "██║   ██║████╗  ██║██║██╔════╝██║  ██║██╔═══██╗╚══██╔══╝\n" +
                        "██║   ██║██╔██╗ ██║██║███████╗███████║██║   ██║   ██║   \n" +
                        "██║   ██║██║╚██╗██║██║╚════██║██╔══██║██║   ██║   ██║   \n" +
                        "╚██████╔╝██║ ╚████║██║███████║██║  ██║╚██████╔╝   ██║   \n" +
                        " ╚═════╝ ╚═╝  ╚═══╝╚═╝╚══════╝╚═╝  ╚═╝ ╚═════╝    ╚═╝   " + RESET);
    }

    /* ---- NLP CHATBOT ---- */
    private static void chatBot() {
        System.out.println(GREEN + "\n🤖 University Chatbot (type 'exit' to quit)");
        Pattern p = Pattern.compile("\\b(list|show|students|courses|sections|grades|gpa|faculty|announcements|report)\\b", Pattern.CASE_INSENSITIVE);
        while (true) {
            System.out.print(CYAN + "You: ");
            String line = sc.nextLine().trim();
            if (line.equalsIgnoreCase("exit")) break;
            Matcher m = p.matcher(line);
            if (line.toLowerCase().contains("students")) {
                listStudents();
            } else if (line.toLowerCase().contains("courses") || line.toLowerCase().contains("sections")) {
                listCourses(); listSections();
            } else if (line.toLowerCase().contains("faculty")) {
                listFaculty();
            } else if (line.toLowerCase().contains("announcements")) {
                listAnnouncements();
            } else if (line.toLowerCase().contains("report")) {
                System.out.println(YELLOW + "Say 'report <student_id>' to generate report.");
            } else if (line.matches("(?i).*report\\s+(\\d+).*")) {
                int sid = Integer.parseInt(line.replaceAll("(?i).*report\\s+(\\d+).*", "$1"));
                generateReport(sid);
            } else if (line.toLowerCase().contains("gpa")) {
                System.out.println(YELLOW + "Use Student portal for GPA.");
            } else {
                System.out.println(YELLOW + "I understand: list students, courses, sections, faculty, announcements, report <id>, or exit.");
            }
        }
    }

    private static void listStudents() {
        try (Connection c = conn();
             Statement st = c.createStatement();
             ResultSet rs = st.executeQuery("SELECT id,name,email FROM student")) {
            System.out.println(GREEN + "\n👨‍🎓 STUDENTS");
            while (rs.next()) System.out.printf("%-2d %-20s  %s%n", rs.getInt(1), rs.getString(2), rs.getString(3));
        } catch (SQLException e) { System.out.println(RED + "DB error"); }
    }

    private static void listFaculty() {
        try (Connection c = conn();
             Statement st = c.createStatement();
             ResultSet rs = st.executeQuery("SELECT id,name,email FROM faculty")) {
            System.out.println(GREEN + "\n👩‍🏫 FACULTY");
            while (rs.next()) System.out.printf("%-2d %-20s  %s%n", rs.getInt(1), rs.getString(2), rs.getString(3));
        } catch (SQLException e) { System.out.println(RED + "DB error"); }
    }

    private static void listAnnouncements() {
        try (Connection c = conn();
             Statement st = c.createStatement();
             ResultSet rs = st.executeQuery(
                     "SELECT f.name, a.message, a.posted_at FROM announcement a JOIN faculty f ON a.faculty_id=f.id ORDER BY a.posted_at DESC")) {
            System.out.println(GREEN + "\n📢 ANNOUNCEMENTS");
            while (rs.next()) System.out.printf("%s: %s (%s)%n", rs.getString(1), rs.getString(2), rs.getString(3));
        } catch (SQLException e) { System.out.println(RED + "DB error"); }
    }

    private static void generateReport(int studentId) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String file = "report_" + studentId + "_" + sdf.format(new java.util.Date()) + ".txt";
        try (Connection c = conn();
             BufferedWriter bw = Files.newBufferedWriter(Paths.get(file));
             PreparedStatement ps = c.prepareStatement(
                     "SELECT c.code,c.title,e.grade FROM enrollment e " +
                             "JOIN section s ON e.section_id=s.id JOIN course c ON s.course_id=c.id " +
                             "WHERE e.student_id=? ORDER BY c.code")) {
            ps.setInt(1, studentId);
            ResultSet rs = ps.executeQuery();
            bw.write("Performance Report\n");
            double tot = 0; int cnt = 0;
            while (rs.next()) {
                bw.write(rs.getString(1) + " - " + rs.getString(2) + " : " + rs.getString(3) + "\n");
                tot += gradeToPoints(rs.getString(3)); cnt++;
            }
            bw.write("GPA: " + (cnt == 0 ? "N/A" : String.format("%.2f", tot / cnt)) + "\n");
            bw.flush();
            try (PreparedStatement em = c.prepareStatement("SELECT email FROM student WHERE id=?")) {
                em.setInt(1, studentId); ResultSet e = em.executeQuery(); e.next();
                notify(e.getString(1), "Admin generated report: " + file);
            }
            System.out.println(GREEN + "✅ Report saved: " + file);
        } catch (Exception e) { System.out.println(RED + "Report failed"); }
    }

    private static double gradeToPoints(String g) {
        return switch (g) { case "A" -> 4; case "B" -> 3; case "C" -> 2; case "D" -> 1; default -> 0; };
    }

    /* ---- main menu ---- */
    private static void mainMenu() {
        banner();
        while (true) {
            System.out.println(CYAN + "\nMAIN MENU");
            System.out.println("1) Student Portal");
            System.out.println("2) Faculty Portal");
            System.out.println("3) Admin Portal");
            System.out.println("4) Chatbot");
            System.out.println("5) Exit");
            switch (readInt()) {
                case 1 -> studentPortal();
                case 2 -> facultyPortal();
                case 3 -> adminPortal();
                case 4 -> chatBot();
                case 5 -> { banner(); System.out.println(GREEN + "👋 Goodbye!"); System.exit(0); }
            }
        }
    }

    /* ---- portals ---- */
    private static void studentPortal() {
        int id = loginUser("student"); if (id == -1) return;
        while (true) {
            System.out.println(CYAN + "\nSTUDENT MENU");
            System.out.println("1) View Courses");
            System.out.println("2) My Sections & GPA");
            System.out.println("3) Register Section");
            System.out.println("4) Drop Section");
            System.out.println("5) Back");
            switch (readInt()) {
                case 1 -> {
                    listCourses();
                    pause();
                }
                case 2 -> {
                    mySections(id);
                    pause();
                }
                case 3 -> registerSection(id);
                case 4 -> dropSection(id);
                case 5 -> { return; }
            }
        }
    }

    private static void facultyPortal() {
        int id = loginUser("faculty"); if (id == -1) return;
        while (true) {
            System.out.println(CYAN + "\nFACULTY MENU");
            System.out.println("1) My Sections");
            System.out.println("2) Enter Grades");
            System.out.println("3) Post Announcement");
            System.out.println("4) Back");
            switch (readInt()) {
                case 1 -> {
                    myFacultySections(id);
                    pause();
                }
                case 2 -> enterGrades(id);
                case 3 -> postAnnouncement(id);
                case 4 -> { return; }
            }
        }
    }

    private static void adminPortal() {
        if (!sc.nextLine().trim().equals("123")) { System.out.println(RED + "❌ Wrong"); pause(); return; }
        while (true) {
            System.out.println(CYAN + "\nADMIN MENU");
            System.out.println("1) List Courses");
            System.out.println("2) List Sections");
            System.out.println("3) Add Course");
            System.out.println("4) Add Section");
            System.out.println("5) Generate Report");
            System.out.println("6) Back");
            switch (readInt()) {
                case 1 -> {
                    listCourses();
                    pause();
                }
                case 2 -> {
                    listSections();
                    pause();
                }
                case 3 -> adminAddCourse();
                case 4 -> adminAddSection();
                case 5 -> generateReportPrompt();
                case 6 -> { return; }
            }
        }
    }

    private static int loginUser(String type) {
        System.out.print("Email: "); String email = sc.nextLine().trim();
        System.out.print("Password: "); String pwd = sc.nextLine().trim();
        try (Connection c = conn();
             PreparedStatement ps = c.prepareStatement(
                     "SELECT id FROM " + type + " WHERE email=? AND pwd=?")) {
            ps.setString(1, email); ps.setString(2, pwd);
            ResultSet rs = ps.executeQuery();
            return rs.next() ? rs.getInt(1) : -1;
        } catch (SQLException e) { return -1; }
    }

    /* ---- CRUD helpers ---- */
    private static void listCourses() {
        System.out.println(PURPLE + "\n📚 COURSES");
        for (Course c : courses)
            System.out.printf("%-2d %-10s %-30s (%d cr)%n", c.id(), c.code(), c.title(), c.credits());
    }

    private static void listSections() {
        System.out.println(PURPLE + "\n📅 SECTIONS");
        for (Section s : sections)
            System.out.printf("%-2d %-10s %-30s %-15s [%d/%d]%n",
                    s.id(), s.code(), s.title(), s.faculty(), s.enrolled(), s.capacity());
    }

    private static void mySections(int studentId) {
        try (Connection c = conn();
             PreparedStatement ps = c.prepareStatement(
                     "SELECT c.code,c.title,e.grade FROM enrollment e " +
                             "JOIN section s ON e.section_id=s.id JOIN course c ON s.course_id=c.id " +
                             "WHERE e.student_id=?")) {
            ResultSet rs = ps.executeQuery();
            double tot = 0; int cnt = 0;
            while (rs.next()) {
                System.out.printf("%-10s %-30s %s%n", rs.getString(1), rs.getString(2), rs.getString(3));
                tot += gradeToPoints(rs.getString(3)); cnt++;
            }
            System.out.println("GPA: " + (cnt == 0 ? "N/A" : String.format("%.2f", tot / cnt)));
        } catch (SQLException e) { System.out.println(RED + "DB error"); }
    }

    private static void myFacultySections(int facultyId) {
        try (Connection c = conn();
             PreparedStatement ps = c.prepareStatement(
                     "SELECT s.id,c.code,c.title FROM section s JOIN course c ON s.course_id=c.id WHERE s.faculty_id=?")) {
            ResultSet rs = ps.executeQuery();
            while (rs.next()) System.out.printf("%-2d %-10s %-30s%n", rs.getInt(1), rs.getString(2), rs.getString(3));
        } catch (SQLException e) { System.out.println(RED + "DB error"); }
    }

    private static void registerSection(int studentId) {
        listSections();
        System.out.print(CYAN + "Section ID to register: ");
        int sid = readInt();
        try (Connection c = conn();
             PreparedStatement ps = c.prepareStatement(
                     "INSERT INTO enrollment(student_id,section_id,grade) VALUES(?,'','')");
             PreparedStatement up = c.prepareStatement("UPDATE section SET enrolled=enrolled+1 WHERE id=?")) {
            ps.setInt(1, studentId); ps.setInt(2, sid); ps.executeUpdate();
            up.setInt(1, sid); up.executeUpdate();
            loadData(); System.out.println(GREEN + "✅ Registered"); pause();
        } catch (SQLException e) { System.out.println(RED + "DB error"); pause(); }
    }

    private static void dropSection(int studentId) {
        System.out.print(CYAN + "Section ID to drop: ");
        int sid = readInt();
        try (Connection c = conn();
             PreparedStatement ps = c.prepareStatement("DELETE FROM enrollment WHERE student_id=? AND section_id=?");
             PreparedStatement up = c.prepareStatement("UPDATE section SET enrolled=enrolled-1 WHERE id=?")) {
            ps.setInt(1, studentId); ps.setInt(2, sid); ps.executeUpdate();
            up.setInt(1, sid); up.executeUpdate();
            loadData(); System.out.println(GREEN + "✅ Dropped"); pause();
        } catch (SQLException e) { System.out.println(RED + "DB error"); pause(); }
    }

    private static void enterGrades(int facultyId) {
        listSections();
        System.out.print(CYAN + "Section ID: ");
        int sid = readInt();
        try (Connection c = conn();
             PreparedStatement ps = c.prepareStatement(
                     "SELECT s.student_id,st.name FROM enrollment s JOIN student st ON s.student_id=st.id WHERE s.section_id=?");
             PreparedStatement up = c.prepareStatement("UPDATE enrollment SET grade=? WHERE student_id=? AND section_id=?")) {
            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                System.out.print("Grade for " + rs.getString(2) + " (A-F): ");
                String grade = sc.nextLine().trim().toUpperCase();
                up.setString(1, grade); up.setInt(2, rs.getInt(1)); up.setInt(3, sid); up.executeUpdate();
            }
            System.out.println(GREEN + "✅ Grades saved"); pause();
        } catch (SQLException e) { System.out.println(RED + "DB error"); pause(); }
    }

    private static void postAnnouncement(int facultyId) {
        System.out.print(CYAN + "Announcement text: ");
        String msg = sc.nextLine().trim();
        try (Connection c = conn();
             PreparedStatement ps = c.prepareStatement("INSERT INTO announcement(faculty_id,message) VALUES(?,?)")) {
            ps.setInt(1, facultyId); ps.setString(2, msg); ps.executeUpdate();
            System.out.println(GREEN + "✅ Posted"); pause();
        } catch (SQLException e) { System.out.println(RED + "DB error"); pause(); }
    }

    private static void adminAddCourse() {
        System.out.print("Code: "); String code = sc.nextLine().trim();
        System.out.print("Title: "); String title = sc.nextLine().trim();
        System.out.print("Credits: "); int credits = readInt();
        try (Connection c = conn();
             PreparedStatement ps = c.prepareStatement("INSERT INTO course(code,title,credits) VALUES(?,?,?)")) {
            ps.setString(1, code); ps.setString(2, title); ps.setInt(3, credits);
            ps.executeUpdate();
            loadData();
            System.out.println(GREEN + "✅ Added"); pause();
        } catch (SQLException e) { System.out.println(RED + "DB error"); pause(); }
    }

    private static void adminAddSection() {
        listCourses();
        System.out.print("Course ID: "); int cid = readInt();
        System.out.print("Faculty ID: "); int fid = readInt();
        System.out.print("Semester: "); String sem = sc.nextLine().trim();
        System.out.print("Year: "); int year = readInt();
        System.out.print("Capacity: "); int cap = readInt();
        try (Connection c = conn();
             PreparedStatement ps = c.prepareStatement(
                     "INSERT INTO section(course_id,faculty_id,semester,year,capacity) VALUES(?,?,?,?,?)")) {
            ps.setInt(1, cid); ps.setInt(2, fid); ps.setString(3, sem); ps.setInt(4, year); ps.setInt(5, cap);
            ps.executeUpdate();
            loadData();
            System.out.println(GREEN + "✅ Added"); pause();
        } catch (SQLException e) { System.out.println(RED + "DB error"); pause(); }
    }

    private static void generateReportPrompt() {
        try (Connection c = conn();
             Statement st = c.createStatement();
             ResultSet rs = st.executeQuery("SELECT id,name FROM student")) {
            while (rs.next()) System.out.printf("%-2d %s%n", rs.getInt(1), rs.getString(2));
            System.out.print("Student ID: ");
            int sid = readInt();
            generateReport(sid);
        } catch (SQLException e) { System.out.println(RED + "DB error"); pause(); }
    }

    private static void generateReport(int studentId) {
        try (Connection c = conn();
             BufferedWriter bw = Files.newBufferedWriter(Paths.get("report_" + studentId + ".txt"));
             PreparedStatement ps = c.prepareStatement(
                     "SELECT c.code,c.title,e.grade FROM enrollment e " +
                             "JOIN section s ON e.section_id=s.id JOIN course c ON s.course_id=c.id " +
                             "WHERE e.student_id=?")) {
            ps.setInt(1, studentId);
            ResultSet rs = ps.executeQuery();
            bw.write("Performance Report\n");
            double tot = 0; int cnt = 0;
            while (rs.next()) {
                bw.write(rs.getString(1) + " - " + rs.getString(2) + " : " + rs.getString(3) + "\n");
                tot += gradeToPoints(rs.getString(3)); cnt++;
            }
            bw.write("GPA: " + (cnt == 0 ? "N/A" : String.format("%.2f", tot / cnt)) + "\n");
            bw.flush();
            try (PreparedStatement em = c.prepareStatement("SELECT email FROM student WHERE id=?")) {
                em.setInt(1, studentId); ResultSet e = em.executeQuery(); e.next();
                notify(e.getString(1), "Admin generated report: report_" + studentId + ".txt");
            }
            System.out.println(GREEN + "✅ Report saved + notification sent"); pause();
        } catch (Exception e) { System.out.println(RED + "Report failed"); pause(); }
    }

    /* ---- main ---- */
    public static void main(String[] args) {
        loadData();
        banner();
        mainMenu();
    }
}