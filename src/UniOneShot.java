import java.io.*;
import java.nio.file.*;
import java.sql.*;
import java.util.*;

public class UniOneShot {

    /* ---- UTF-8 on Windows ---- */
    static { try { new ProcessBuilder("cmd", "/c", "chcp 65001 > nul").inheritIO().start().waitFor(); } catch (Exception ignored) {} }

    /* ---- JDBC ---- */
    private static Connection conn() throws SQLException {
        return DriverManager.getConnection(
                "****************************************************************", "root", "");
    }

    /* ---- models ---- */
    private record Student(int id, String email, String name) {}
    private record Faculty(int id, String email, String name) {}
    private record Course(int id,String code,String title,int credits,boolean deleted) {}
    private record Section(int id,int courseId,String code,String title,int credits,String faculty,int capacity,int enrolled,boolean deleted) {}

    /* ---- caches ---- */
    private static final List<Course>  courses  = new ArrayList<>();
    private static final List<Section> sections = new ArrayList<>();
    private static final Scanner sc = new Scanner(System.in);

    /* ---- colour ---- */
    private static final String RESET  = "\u001B[0m";
    private static final String GREEN  = "\u001B[32m";
    private static final String RED    = "\u001B[31m";
    private static final String CYAN   = "\u001B[36m";
    private static final String YELLOW = "\u001B[33m";
    private static final String PURPLE = "\u001B[35m";

    /* ---- helpers ---- */
    private static void pause() { System.out.print(GREEN + "\n✔ Press ENTER…"); sc.nextLine(); }
    private static int readInt() {
        while (true) try { return Integer.parseInt(sc.nextLine().trim()); }
        catch (Exception e) { System.out.print(RED + "Enter integer: "); }
    }

    /* ---- main ---- */
    public static void main(String[] args) { loadData(); mainMenu(); }

    /* ---- loaders ---- */
    private static void loadData() {
        courses.clear(); sections.clear();
        try (Connection c = conn()) {
            try (Statement st = c.createStatement();
                 ResultSet rs = st.executeQuery("SELECT id,code,title,credits,deleted FROM course WHERE deleted=0")) {
                while (rs.next()) courses.add(new Course(rs.getInt(1), rs.getString(2), rs.getString(3), rs.getInt(4), rs.getBoolean(5)));
            }
            try (Statement st = c.createStatement();
                 ResultSet rs = st.executeQuery(
                         "SELECT s.id,s.course_id,c.code,c.title,c.credits,f.name,s.capacity,s.enrolled,s.deleted " +
                                 "FROM section s JOIN course c ON s.course_id=c.id JOIN faculty f ON s.faculty_id=f.id WHERE s.deleted=0")) {
                while (rs.next()) sections.add(new Section(rs.getInt(1), rs.getInt(2), rs.getString(3), rs.getString(4),
                        rs.getInt(5), rs.getString(6), rs.getInt(7), rs.getInt(8), rs.getBoolean(9)));
            }
        } catch (SQLException e) { System.out.println(RED + "DB load error"); }
    }

    /* ---- main menu ---- */
    private static void mainMenu() {
        while (true) {
            System.out.println(CYAN + "\n🎓 UNIVERSITY CLI");
            System.out.println("1) Student Portal");
            System.out.println("2) Faculty Portal");
            System.out.println("3) Admin Portal");
            System.out.println("4) Exit");
            switch (readInt()) {
                case 1 -> studentPortal();
                case 2 -> facultyPortal();
                case 3 -> adminPortal();
                case 4 -> { System.out.println(GREEN + "👋 Goodbye!"); System.exit(0); }
            }
        }
    }

    /* ---- STUDENT ---- */
    private static void studentPortal() {
        System.out.println(CYAN + "\n👨‍🎓 STUDENT LOGIN");
        System.out.print("Email: "); String email = sc.nextLine().trim();
        System.out.print("Password: "); String pwd = sc.nextLine().trim();
        try (Connection c = conn();
             PreparedStatement ps = c.prepareStatement(
                     "SELECT id,name FROM student WHERE email=? AND pwd=?")) {
            ps.setString(1, email); ps.setString(2, pwd);
            ResultSet rs = ps.executeQuery();
            if (!rs.next()) { System.out.println(RED + "❌ Invalid"); pause(); return; }
            int id = rs.getInt(1); String name = rs.getString(2);
            while (true) {
                System.out.println(CYAN + "\nSTUDENT: " + name);
                System.out.println("1) View Courses/Sections");
                System.out.println("2) Register Section");
                System.out.println("3) Drop Section");
                System.out.println("4) Logout");
                switch (readInt()) {
                    case 1 -> {
                        listCourses();
                        pause();
                    }
                    case 2 -> registerSection(id);
                    case 3 -> dropSection(id);
                    case 4 -> { return; }
                }
            }
        } catch (SQLException e) { System.out.println(RED + "DB error"); pause(); }
    }

    /* ---- FACULTY ---- */
    private static void facultyPortal() {
        System.out.println(CYAN + "\n👨‍🏫 FACULTY LOGIN");
        System.out.print("Email: "); String email = sc.nextLine().trim();
        System.out.print("Password: "); String pwd = sc.nextLine().trim();
        try (Connection c = conn();
             PreparedStatement ps = c.prepareStatement(
                     "SELECT id,name FROM faculty WHERE email=? AND pwd=?")) {
            ps.setString(1, email); ps.setString(2, pwd);
            ResultSet rs = ps.executeQuery();
            if (!rs.next()) { System.out.println(RED + "❌ Invalid"); pause(); return; }
            int id = rs.getInt(1); String name = rs.getString(2);
            while (true) {
                System.out.println(CYAN + "\nFACULTY: " + name);
                System.out.println("1) My Sections");
                System.out.println("2) Enter Grades");
                System.out.println("3) Logout");
                switch (readInt()) {
                    case 1 -> {
                        myFacultySections(name);
                        pause();
                    }
                    case 2 -> enterGrades(name);
                    case 3 -> { return; }
                }
            }
        } catch (SQLException e) { System.out.println(RED + "DB error"); pause(); }
    }

    /* ---- ADMIN ---- */
    private static void adminPortal() {
        System.out.println(CYAN + "\n⚙️ ADMIN LOGIN");
        System.out.print("Password (123): ");
        if (!sc.nextLine().trim().equals("123")) { System.out.println(RED + "❌ Wrong"); pause(); return; }
        while (true) {
            System.out.println(CYAN + "\nADMIN MENU");
            System.out.println("1) List Courses");
            System.out.println("2) List Sections");
            System.out.println("3) Add Course");
            System.out.println("4) Add Section");
            System.out.println("5) Generate Report");
            System.out.println("6) Back");
            switch (readInt()) {
                case 1 -> {
                    listCourses();
                    pause();
                }
                case 2 -> {
                    listSections();
                    pause();
                }
                case 3 -> adminAddCourse();
                case 4 -> adminAddSection();
                case 5 -> generateReport();
                case 6 -> { return; }
            }
        }
    }

    /* ---- list helpers ---- */
    private static void listCourses() {
        System.out.println(PURPLE + "\n📚 COURSES");
        for (Course c : courses)
            System.out.printf("%-2d %-10s %-30s (%d cr)%n", c.id(), c.code(), c.title(), c.credits());
    }

    private static void listSections() {
        System.out.println(PURPLE + "\n📅 SECTIONS");
        for (Section s : sections)
            System.out.printf("%-2d %-10s %-30s %-15s [%d/%d]%n",
                    s.id(), s.code(), s.title(), s.faculty(), s.enrolled(), s.capacity());
    }

    /* ---- CRUD ---- */
    private static void registerSection(int studentId) {
        listSections();
        System.out.print(CYAN + "Section ID to register: ");
        int sid = readInt();
        try (Connection c = conn();
             PreparedStatement ps = c.prepareStatement(
                     "INSERT INTO enrollment(student_id,section_id,grade) VALUES(?,'','')")) {
            ps.setInt(1, studentId); ps.setInt(2, sid);
            ps.executeUpdate();
            c.prepareStatement("UPDATE section SET enrolled=enrolled+1 WHERE id=?").setInt(1, sid).executeUpdate();
            loadData();
            System.out.println(GREEN + "✅ Registered"); pause();
        } catch (SQLException e) { System.out.println(RED + "DB error"); pause(); }
    }

    private static void dropSection(int studentId) {
        System.out.print(CYAN + "Section ID to drop: ");
        int sid = readInt();
        try (Connection c = conn();
             PreparedStatement ps = c.prepareStatement("DELETE FROM enrollment WHERE student_id=? AND section_id=?")) {
            ps.setInt(1, studentId); ps.setInt(2, sid);
            if (ps.executeUpdate() == 0) System.out.println(YELLOW + "Not found");
            else { c.prepareStatement("UPDATE section SET enrolled=enrolled-1 WHERE id=?").setInt(1, sid).executeUpdate(); loadData(); System.out.println(GREEN + "✅ Dropped"); }
            pause();
        } catch (SQLException e) { System.out.println(RED + "DB error"); pause(); }
    }

    private static void myFacultySections(String name) {
        System.out.println(PURPLE + "\n📋 MY SECTIONS");
        for (Section s : sections) if (s.faculty().equals(name))
            System.out.printf("%-2d %-10s %-30s [%d/%d]%n", s.id(), s.code(), s.title(), s.enrolled(), s.capacity());
    }

    private static void enterGrades(String name) {
        myFacultySections(name);
        System.out.print(CYAN + "Section ID: ");
        int sid = readInt();
        try (Connection c = conn();
             PreparedStatement ps = c.prepareStatement(
                     "SELECT s.student_id,st.name FROM enrollment s JOIN student st ON s.student_id=st.id WHERE s.section_id=?")) {
            ps.setInt(1, sid);
            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                System.out.print("Grade for " + rs.getString(2) + " (A-F): ");
                String grade = sc.nextLine().trim().toUpperCase();
                PreparedStatement up = c.prepareStatement("UPDATE enrollment SET grade=? WHERE student_id=? AND section_id=?");
                up.setString(1, grade); up.setInt(2, rs.getInt(1)); up.setInt(3, sid); up.executeUpdate();
            }
            System.out.println(GREEN + "✅ Grades saved"); pause();
        } catch (SQLException e) { System.out.println(RED + "DB error"); pause(); }
    }

    private static void adminAddCourse() {
        System.out.print(CYAN + "Code: "); String code = sc.nextLine().trim();
        System.out.print("Title: "); String title = sc.nextLine().trim();
        System.out.print("Credits: "); int credits = readInt();
        try (Connection c = conn();
             PreparedStatement ps = c.prepareStatement("INSERT INTO course(code,title,credits) VALUES(?,?,?)")) {
            ps.setString(1, code); ps.setString(2, title); ps.setInt(3, credits);
            ps.executeUpdate();
            loadData();
            System.out.println(GREEN + "✅ Added"); pause();
        } catch (SQLException e) { System.out.println(RED + "DB error"); pause(); }
    }

    private static void adminAddSection() {
        listCourses();
        System.out.print(CYAN + "Course ID: "); int cid = readInt();
        System.out.print("Faculty ID: "); int fid = readInt();
        System.out.print("Semester: "); String sem = sc.nextLine().trim();
        System.out.print("Year: "); int year = readInt();
        System.out.print("Capacity: "); int cap = readInt();
        try (Connection c = conn();
             PreparedStatement ps = c.prepareStatement(
                     "INSERT INTO section(course_id,faculty_id,semester,year,capacity) VALUES(?,?,?,?,?)")) {
            ps.setInt(1, cid); ps.setInt(2, fid); ps.setString(3, sem);
            ps.setInt(4, year); ps.setInt(5, cap);
            ps.executeUpdate();
            loadData();
            System.out.println(GREEN + "✅ Added"); pause();
        } catch (SQLException e) { System.out.println(RED + "DB error"); pause(); }
    }

    private static void generateReport() {
        try (Connection c = conn();
             Statement st = c.createStatement();
             ResultSet rs = st.executeQuery("SELECT s.id,st.name FROM student st JOIN enrollment s ON st.id=s.student_id GROUP BY st.id")) {
            while (rs.next()) {
                String file = "report_" + rs.getInt(1) + "_" + System.currentTimeMillis() + ".txt";
                try (BufferedWriter bw = Files.newBufferedWriter(Paths.get(file));
                     PreparedStatement grades = c.prepareStatement(
                             "SELECT c.code,c.title,e.grade FROM enrollment e " +
                                     "JOIN section sec ON e.section_id=sec.id JOIN course c ON sec.course_id=c.id " +
                                     "WHERE e.student_id=?")) {
                    grades.setInt(1, rs.getInt(1));
                    ResultSet g = grades.executeQuery();
                    bw.write("Report for " + rs.getString(2) + "\n");
                    while (g.next()) bw.write(g.getString(1) + " - " + g.getString(2) + " : " + g.getString(3) + "\n");
                }
                notify(rs.getString(2), "Your report is ready: " + file);
            }
            System.out.println(GREEN + "✅ Reports generated"); pause();
        } catch (Exception e) { System.out.println(RED + "Report failed"); pause(); }
    }

    private static void notify(String email, String msg) {
        try (Connection c = conn();
             PreparedStatement ps = c.prepareStatement("INSERT INTO notification(user_email,message) VALUES(?,?)")) {
            ps.setString(1, email); ps.setString(2, msg); ps.executeUpdate();
        } catch (SQLException ignored) {}
    }

    private static void showNotifications(String email) {
        try (Connection c = conn();
             PreparedStatement ps = c.prepareStatement("SELECT message FROM notification WHERE user_email=? AND is_read=0")) {
            ps.setString(1, email);
            ResultSet rs = ps.executeQuery();
            while (rs.next()) System.out.println(GREEN + rs.getString(1));
            c.prepareStatement("UPDATE notification SET is_read=1 WHERE user_email=?").setString(1, email).executeUpdate();
        } catch (SQLException ignored) {}
    }
}